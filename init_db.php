<?php
require_once 'config/database.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // Créer la base de données si elle n'existe pas
    $pdo->exec("CREATE DATABASE IF NOT EXISTS journal_scientifique");
    $pdo->exec("USE journal_scientifique");
    
    // Créer les tables
    $sql = file_get_contents('donnees.sql');
    $pdo->exec($sql);
    
    echo "Base de données initialisée avec succès !\n";
    
} catch(PDOException $e) {
    echo "Erreur : " . $e->getMessage();
}
