<?php
// Configuration de base
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Définir le fuseau horaire
date_default_timezone_set('Europe/Paris');

// Définir le chemin de base
define('BASE_PATH', realpath(__DIR__));

// Inclure les fichiers de base
require_once BASE_PATH . '/config/database.php';
require_once BASE_PATH . '/config/config.php';
require_once BASE_PATH . '/config/site.php';
require_once BASE_PATH . '/core/helpers.php'; // Ajout des helpers

// Autoloader amélioré
spl_autoload_register(function($class) {
    // Chercher dans core/
    $coreFile = BASE_PATH . '/core/' . $class . '.php';
    if (file_exists($coreFile)) {
        require_once $coreFile;
        return;
    }
    
    // Chercher dans models/
    $modelFile = BASE_PATH . '/models/' . $class . '.php';
    if (file_exists($modelFile)) {
        require_once $modelFile;
        return;
    }
    
    // Chercher dans controllers/
    $controllerFile = BASE_PATH . '/controllers/' . $class . '.php';
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        return;
    }
});

// Définir les chemins
define('VIEWS_PATH', BASE_PATH . '/views');
define('CONTROLLERS_PATH', BASE_PATH . '/controllers');
define('MODELS_PATH', BASE_PATH . '/models');
define('UPLOADS_PATH', BASE_PATH . '/uploads/articles');

// Démarrer la session
session_start();

