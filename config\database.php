<?php
class Database {
    private static $instance = null;
    private $connection;
    
    // Informations de connexion à la base de données
    private $host = 'localhost';
    private $username = 'root';
    private $password = '';  // Mot de passe vide pour le développement local
    private $database = 'journal_scientifique';
    private $port = 3306;    // Port MySQL standard
    
    private function __construct() {
        try {
            $this->connection = new PDO(
                "mysql:host={$this->host};port={$this->port};dbname={$this->database};charset=utf8",
                $this->username,
                $this->password,
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
        } catch(PDOException $e) {
            die("Erreur de connexion : " . $e->getMessage());
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
}
?>
