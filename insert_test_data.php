<?php
require_once 'init.php';

try {
    $db = Database::getInstance()->getConnection();
    
    // Insérer un évaluateur de test s'il n'existe pas
    $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    if ($stmt->fetchColumn() == 0) {
        $stmt = $db->prepare("INSERT INTO users (firstname, lastname, email, password, role, institution) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['Jean', 'Dupont', '<EMAIL>', password_hash('password', PASSWORD_DEFAULT), 'evaluateur', 'Université de Paris']);
        echo "Évaluateur de test créé.\n";
    }
    
    // Insérer des volumes de test s'ils n'existent pas
    $stmt = $db->prepare("SELECT COUNT(*) FROM volumes");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        $stmt = $db->prepare("INSERT INTO volumes (number, year, title, description, publication_date, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([1, 2024, 'Volume 1 - Sciences et Technologies', 'Premier volume de la revue scientifique', '2024-01-15']);
        $stmt->execute([2, 2024, 'Volume 2 - Recherche Interdisciplinaire', 'Deuxième volume axé sur la recherche interdisciplinaire', '2024-06-15']);
        echo "Volumes de test créés.\n";
    }
    
    // Insérer un article de test avec statut "accepté"
    $stmt = $db->prepare("SELECT COUNT(*) FROM articles WHERE status = 'accepte'");
    $stmt->execute();
    if ($stmt->fetchColumn() == 0) {
        // D'abord, récupérer l'ID de l'éditeur
        $stmt = $db->prepare("SELECT id FROM users WHERE role = 'editeur' LIMIT 1");
        $stmt->execute();
        $editorId = $stmt->fetchColumn();
        
        if ($editorId) {
            $stmt = $db->prepare("INSERT INTO articles (title, abstract, keywords, domain_id, author_id, file_path, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                'Intelligence Artificielle et Machine Learning',
                'Cet article explore les dernières avancées en intelligence artificielle et machine learning, avec un focus sur les applications pratiques dans le domaine de la recherche scientifique.',
                'intelligence artificielle, machine learning, recherche, technologie',
                1, // Informatique
                $editorId,
                'uploads/articles/test_article.pdf',
                'accepte'
            ]);
            echo "Article de test créé avec statut 'accepté'.\n";
        }
    }
    
    echo "Données de test insérées avec succès !\n";
    echo "Vous pouvez maintenant tester la fonctionnalité de publication.\n";
    
} catch (Exception $e) {
    echo "Erreur : " . $e->getMessage() . "\n";
}
?>
