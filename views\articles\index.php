<?php include 'views/layout/header.php'; ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Mes Articles</h1>
    <a href="<?= BASE_URL ?>articles/submit" class="btn btn-success">Soumettre un article</a>
</div>

<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <?php if ($_GET['success'] == '1'): ?>
            <i class="fas fa-check-circle"></i> Article soumis avec succès !
        <?php elseif ($_GET['success'] == 'updated'): ?>
            <i class="fas fa-check-circle"></i> Article modifié avec succès !
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <?php if ($_GET['error'] == 'cannot_edit'): ?>
            <i class="fas fa-exclamation-triangle"></i> Impossible de modifier cet article. Seuls les articles avec le statut "soumis" peuvent être modifiés.
        <?php endif; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!empty($articles)): ?>
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Titre</th>
                    <th>Domaine</th>
                    <th>Statut</th>
                    <th>Date de soumission</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($articles as $article): ?>
                    <tr>
                        <td><?= htmlspecialchars($article['title']) ?></td>
                        <td><?= htmlspecialchars($article['domain_name'] ?? 'Non spécifié') ?></td>
                        <td>
                            <span class="badge status-<?= $article['status'] ?>">
                                <?= ucfirst(str_replace('_', ' ', $article['status'])) ?>
                            </span>
                        </td>
                        <td><?= date('d/m/Y', strtotime($article['created_at'])) ?></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="<?= BASE_URL ?>articles/view?id=<?= $article['id'] ?>"
                                   class="btn btn-primary" title="Voir les détails">
                                    <i class="fas fa-eye"></i> Voir
                                </a>
                                <?php if ($article['status'] === 'soumis'): ?>
                                    <a href="<?= BASE_URL ?>articles/edit?id=<?= $article['id'] ?>"
                                       class="btn btn-warning" title="Modifier l'article">
                                        <i class="fas fa-edit"></i> Modifier
                                    </a>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <div class="text-center py-5">
        <h3>Aucun article</h3>
        <p>Vous n'avez pas encore soumis d'articles.</p>
        <a href="<?= BASE_URL ?>articles/submit" class="btn btn-success">Soumettre votre premier article</a>
    </div>
<?php endif; ?>

<?php include 'views/layout/footer.php'; ?>