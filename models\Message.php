<?php
class Message extends Model {
    protected $table = 'messages';
    
    public function getByUser($userId) {
        $stmt = $this->db->prepare("
            SELECT m.*, a.title as article_title,
                   CASE WHEN m.is_anonymous = 1 THEN 'Anonyme' 
                        ELSE CONCAT(u.firstname, ' ', u.lastname) END as sender_name
            FROM messages m 
            JOIN articles a ON m.article_id = a.id 
            LEFT JOIN users u ON m.sender_id = u.id 
            WHERE m.recipient_id = ? OR m.sender_id = ? 
            ORDER BY m.created_at DESC
        ");
        $stmt->execute([$userId, $userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getUnreadCount($userId) {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count 
            FROM messages 
            WHERE recipient_id = ? AND is_read = 0
        ");
        $stmt->execute([$userId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }
    
    public function markAsRead($messageId, $userId) {
        $stmt = $this->db->prepare("
            UPDATE messages 
            SET is_read = 1 
            WHERE id = ? AND recipient_id = ?
        ");
        return $stmt->execute([$messageId, $userId]);
    }
}
?>