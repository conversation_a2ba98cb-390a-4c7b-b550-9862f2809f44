<?php include 'views/layout/header.php'; ?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-edit"></i> Modifier l'article</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                
                    <form method="POST" enctype="multipart/form-data" class="needs-validation" id="edit-article-form" novalidate>
                        <div class="mb-3">
                            <label for="title" class="form-label">Titre de l'article *</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="<?= htmlspecialchars($article['title']) ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="abstract" class="form-label">Résumé *</label>
                            <textarea class="form-control" id="abstract" name="abstract" rows="6" required><?= htmlspecialchars($article['abstract']) ?></textarea>
                            <div class="form-text">Maximum 500 mots</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="keywords" class="form-label">Mots-clés</label>
                            <input type="text" class="form-control" id="keywords" name="keywords" 
                                   value="<?= htmlspecialchars($article['keywords']) ?>">
                            <div class="form-text">Séparez les mots-clés par des virgules</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="domain" class="form-label">Domaine de recherche</label>
                            <select class="form-select" id="domain" name="domain">
                                <option value="">Sélectionnez un domaine</option>
                                <option value="informatique" <?= $article['domain'] === 'informatique' ? 'selected' : '' ?>>Informatique</option>
                                <option value="mathematiques" <?= $article['domain'] === 'mathematiques' ? 'selected' : '' ?>>Mathématiques</option>
                                <option value="physique" <?= $article['domain'] === 'physique' ? 'selected' : '' ?>>Physique</option>
                                <option value="chimie" <?= $article['domain'] === 'chimie' ? 'selected' : '' ?>>Chimie</option>
                                <option value="biologie" <?= $article['domain'] === 'biologie' ? 'selected' : '' ?>>Biologie</option>
                                <option value="medecine" <?= $article['domain'] === 'medecine' ? 'selected' : '' ?>>Médecine</option>
                                <option value="ingenierie" <?= $article['domain'] === 'ingenierie' ? 'selected' : '' ?>>Ingénierie</option>
                                <option value="sciences_sociales" <?= $article['domain'] === 'sciences_sociales' ? 'selected' : '' ?>>Sciences Sociales</option>
                                <option value="economie" <?= $article['domain'] === 'economie' ? 'selected' : '' ?>>Économie</option>
                                <option value="psychologie" <?= $article['domain'] === 'psychologie' ? 'selected' : '' ?>>Psychologie</option>
                                <option value="autre" <?= $article['domain'] === 'autre' ? 'selected' : '' ?>>Autre</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Fichier PDF actuel</label>
                            <div class="alert alert-info">
                                <i class="fas fa-file-pdf"></i> 
                                Fichier actuel : <?= basename($article['file_path']) ?>
                                <?php if (file_exists($article['file_path'])): ?>
                                    <a href="<?= BASE_URL . str_replace(BASE_PATH, '', $article['file_path']) ?>" 
                                       target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                                        <i class="fas fa-eye"></i> Voir
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="pdf" class="form-label">Nouveau fichier PDF (optionnel)</label>
                            <input type="file" class="form-control" id="pdf" name="pdf" accept=".pdf">
                            <div class="form-text">
                                Laissez vide pour conserver le fichier actuel. 
                                Formats acceptés : PDF uniquement. Taille maximale : 10 MB.
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="<?= BASE_URL ?>articles" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> Enregistrer les modifications
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.getElementById('edit-article-form');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Validation du fichier PDF
    const pdfInput = document.getElementById('pdf');
    pdfInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            // Vérifier l'extension
            const fileName = file.name.toLowerCase();
            if (!fileName.endsWith('.pdf')) {
                alert('Seuls les fichiers PDF sont autorisés.');
                this.value = '';
                return;
            }
            
            // Vérifier la taille (10 MB = 10 * 1024 * 1024 bytes)
            if (file.size > 10 * 1024 * 1024) {
                alert('Le fichier est trop volumineux. Taille maximale : 10 MB.');
                this.value = '';
                return;
            }
        }
    });
});
</script>

<?php include 'views/layout/footer.php'; ?>
