<?php include 'views/layout/header.php'; ?>

<div class="hero-section bg-primary text-white py-5 mb-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8">
                <h1 class="display-4">Journal Scientifique Universitaire</h1>
                <p class="lead">Revue scientifique multidisciplinaire dédiée à la recherche universitaire</p>
                <a href="<?= BASE_URL ?>articles" class="btn btn-light btn-lg">Parcourir les articles</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <h2>Articles récents</h2>
        <?php if (!empty($recentArticles)): ?>
            <?php foreach ($recentArticles as $article): ?>
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($article['title']) ?></h5>
                        <p class="card-text"><?= substr(htmlspecialchars($article['abstract']), 0, 200) ?>...</p>
                        <p class="card-text">
                            <small class="text-muted">
                                Par <?= htmlspecialchars($article['firstname'] . ' ' . $article['lastname']) ?>
                                - <?= date('d/m/Y', strtotime($article['published_at'])) ?>
                            </small>
                        </p>
                        <a href="<?= BASE_URL ?>articles/view?id=<?= $article['id'] ?>" class="btn btn-primary">Lire l'article</a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>Aucun article publié pour le moment.</p>
        <?php endif; ?>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5>Recherche</h5>
            </div>
            <div class="card-body">
                <form action="<?= BASE_URL ?>search" method="GET">
                    <div class="mb-3">
                        <input type="text" class="form-control" name="q" placeholder="Mots-clés...">
                    </div>
                    <div class="mb-3">
                        <select class="form-select" name="domain">
                            <option value="">Tous les domaines</option>
                            <?php foreach ($domains as $domain): ?>
                                <option value="<?= $domain['id'] ?>"><?= htmlspecialchars($domain['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Rechercher</button>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>Domaines</h5>
            </div>
            <div class="card-body">
                <?php foreach ($domains as $domain): ?>
                    <span class="badge bg-secondary me-2 mb-2"><?= htmlspecialchars($domain['name']) ?></span>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<?php include 'views/layout/footer.php'; ?>