body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: url('../images/image1.jpeg') no-repeat center center fixed;
    background-size: cover;
    color: white;
}

header {
    background-color: rgba(0, 0, 0, 0.7);
    padding: 20px;
    text-align: center;
}

h1 {
    font-size: 2.5em;
    margin: 0;
}

.container {
    max-width: 1200px;
    margin: auto;
    padding: 20px;
}

.button {
    background-color: rgba(255, 255, 255, 0.8);
    color: black;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.button:hover {
    background-color: rgba(255, 255, 255, 1);
}

.footer {
    text-align: center;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    position: relative;
    bottom: 0;
    width: 100%;
}