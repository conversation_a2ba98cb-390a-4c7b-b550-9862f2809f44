<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test du bouton Assigner Évaluateur</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test du bouton Assigner Évaluateur</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5>Article de test</h5>
                        <p>Titre: "Analyse des Données et Big Data"</p>
                        <p>Statut: soumis</p>
                        
                        <button class="btn btn-outline-warning assign-btn" 
                                data-id="2"
                                title="Assigner un évaluateur">
                            <i class="fas fa-user-plus"></i> Assigner
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour assigner un évaluateur -->
    <div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignModalLabel">Assigner un Évaluateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignForm">
                        <input type="hidden" id="assign_article_id" name="article_id">
                        <div class="mb-3">
                            <label for="evaluator_id" class="form-label">Évaluateur</label>
                            <select class="form-select" id="evaluator_id" name="evaluator_id" required>
                                <option value="">Choisir un évaluateur...</option>
                                <option value="2">Jean Dupont</option>
                                <option value="3">Marie Martin</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="deadline" class="form-label">Date limite d'évaluation</label>
                            <input type="date" class="form-control" id="deadline" name="deadline" required>
                        </div>
                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instructions pour l'évaluateur</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="3" 
                                      placeholder="Instructions spécifiques pour cette évaluation..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-warning" id="confirmAssign">Assigner</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page chargée, initialisation des événements...');
            
            // Gestionnaires pour les boutons d'assignation
            document.querySelectorAll('.assign-btn').forEach(button => {
                console.log('Bouton d\'assignation trouvé:', button);
                button.addEventListener('click', function() {
                    console.log('Bouton assigner cliqué !');
                    const articleId = this.getAttribute('data-id');
                    console.log('ID de l\'article:', articleId);
                    
                    document.getElementById('assign_article_id').value = articleId;
                    
                    // Définir la date limite par défaut (dans 2 semaines)
                    const deadline = new Date();
                    deadline.setDate(deadline.getDate() + 14);
                    document.getElementById('deadline').value = deadline.toISOString().split('T')[0];
                    
                    // Ouvrir la modal
                    const modal = new bootstrap.Modal(document.getElementById('assignModal'));
                    modal.show();
                });
            });

            // Confirmation d'assignation
            document.getElementById('confirmAssign').addEventListener('click', function() {
                console.log('Confirmation d\'assignation cliquée');
                const form = document.getElementById('assignForm');
                const formData = new FormData(form);
                
                console.log('Données du formulaire:', {
                    article_id: formData.get('article_id'),
                    evaluator_id: formData.get('evaluator_id'),
                    deadline: formData.get('deadline'),
                    instructions: formData.get('instructions')
                });
                
                alert('Test réussi ! L\'évaluateur serait assigné.');
            });
        });
    </script>
</body>
</html>
