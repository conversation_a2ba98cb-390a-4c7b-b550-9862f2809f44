<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des boutons de publication</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test des boutons de publication</h1>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5>Article de test</h5>
                        <p>Titre: "Intelligence Artificielle et Machine Learning"</p>
                        <p>Statut: accepté</p>
                        
                        <button class="btn btn-outline-success publish-btn" data-id="1">
                            <i class="fas fa-publish"></i> Publier
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour publier un article -->
    <div class="modal fade" id="publishModal" tabindex="-1" aria-labelledby="publishModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="publishModalLabel">Publier un Article</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="publishForm">
                        <input type="hidden" id="publish_article_id" name="article_id">
                        <div class="mb-3">
                            <label for="volume_id" class="form-label">Volume</label>
                            <select class="form-select" id="volume_id" name="volume_id" required>
                                <option value="">Sélectionner un volume...</option>
                                <option value="1">Volume 1 - 2024 (Sciences et Technologies)</option>
                                <option value="2">Volume 2 - 2024 (Recherche Interdisciplinaire)</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="page_start" class="form-label">Page de début</label>
                                    <input type="number" class="form-control" id="page_start" name="page_start" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="page_end" class="form-label">Page de fin</label>
                                    <input type="number" class="form-control" id="page_end" name="page_end" min="1" required>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-success" id="confirmPublish">Publier</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Gestionnaires pour les boutons de publication
            document.querySelectorAll('.publish-btn').forEach(button => {
                button.addEventListener('click', function() {
                    console.log('Bouton publier cliqué !');
                    const articleId = this.getAttribute('data-id');
                    document.getElementById('publish_article_id').value = articleId;
                    
                    // Ouvrir la modal
                    const modal = new bootstrap.Modal(document.getElementById('publishModal'));
                    modal.show();
                });
            });

            // Confirmation de publication
            document.getElementById('confirmPublish').addEventListener('click', function() {
                const form = document.getElementById('publishForm');
                const formData = new FormData(form);
                
                // Validation des pages
                const pageStart = parseInt(document.getElementById('page_start').value);
                const pageEnd = parseInt(document.getElementById('page_end').value);
                
                if (pageEnd <= pageStart) {
                    alert('La page de fin doit être supérieure à la page de début');
                    return;
                }
                
                console.log('Données du formulaire:', {
                    article_id: formData.get('article_id'),
                    volume_id: formData.get('volume_id'),
                    page_start: formData.get('page_start'),
                    page_end: formData.get('page_end')
                });
                
                alert('Test réussi ! Les données seraient envoyées au serveur.');
            });
        });
    </script>
</body>
</html>
