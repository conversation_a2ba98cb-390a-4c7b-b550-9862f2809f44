<?php
class AdminController extends Controller {
    
    public function index() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $articleModel = $this->loadModel('Article');
        $userModel = $this->loadModel('User');
        $volumeModel = $this->loadModel('Volume');
        
        $stats = [
            'totalArticles' => $articleModel->getTotalCount(),
            'submittedArticles' => count($articleModel->getByStatus('soumis')),
            'publishedArticles' => count($articleModel->getByStatus('publie')),
            'totalUsers' => $userModel->getTotalCount(),
            'totalVolumes' => $volumeModel->getTotalCount()
        ];
        
        $this->loadView('admin/index', ['stats' => $stats]);
    }
    
    public function articles() {
        $this->requireLogin();

        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }

        $articleModel = $this->loadModel('Article');
        $userModel = $this->loadModel('User');
        $volumeModel = $this->loadModel('Volume');

        $articles = $articleModel->getAllWithDetails();
        $evaluators = $userModel->getByRole('evaluateur');
        $volumes = $volumeModel->findAll();

        $this->loadView('admin/articles', [
            'articles' => $articles,
            'evaluators' => $evaluators,
            'volumes' => $volumes
        ]);
    }

    public function users() {
        $this->requireLogin();

        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }

        $userModel = $this->loadModel('User');
        $users = $userModel->findAll();

        $this->loadView('admin/users', ['users' => $users]);
    }

    public function createUser() {
        $this->requireLogin();

        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userModel = $this->loadModel('User');

            $userData = [
                'firstname' => $_POST['firstname'] ?? '',
                'lastname' => $_POST['lastname'] ?? '',
                'email' => $_POST['email'] ?? '',
                'password' => $_POST['password'] ?? '',
                'role' => $_POST['role'] ?? 'auteur',
                'institution' => $_POST['institution'] ?? '',
                'speciality' => $_POST['speciality'] ?? ''
            ];

            // Validation
            $errors = [];
            if (empty($userData['firstname'])) $errors[] = 'Le prénom est requis';
            if (empty($userData['lastname'])) $errors[] = 'Le nom est requis';
            if (empty($userData['email'])) $errors[] = 'L\'email est requis';
            if (empty($userData['password'])) $errors[] = 'Le mot de passe est requis';

            // Vérifier si l'email existe déjà
            if ($userModel->findByEmail($userData['email'])) {
                $errors[] = 'Cet email est déjà utilisé';
            }

            if (empty($errors)) {
                if ($userModel->createUser($userData)) {
                    echo json_encode(['success' => true, 'message' => 'Utilisateur créé avec succès']);
                } else {
                    echo json_encode(['success' => false, 'message' => 'Erreur lors de la création']);
                }
            } else {
                echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
            }
        }
    }

    public function updateUser() {
        $this->requireLogin();

        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userModel = $this->loadModel('User');
            $userId = $_POST['user_id'] ?? 0;

            $userData = [
                'firstname' => $_POST['firstname'] ?? '',
                'lastname' => $_POST['lastname'] ?? '',
                'email' => $_POST['email'] ?? '',
                'role' => $_POST['role'] ?? 'auteur',
                'institution' => $_POST['institution'] ?? '',
                'speciality' => $_POST['speciality'] ?? ''
            ];

            // Si un nouveau mot de passe est fourni
            if (!empty($_POST['password'])) {
                $userData['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
            }

            if ($userModel->update($userId, $userData)) {
                echo json_encode(['success' => true, 'message' => 'Utilisateur modifié avec succès']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la modification']);
            }
        }
    }

    public function deleteUser() {
        $this->requireLogin();

        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userModel = $this->loadModel('User');
            $userId = $_POST['user_id'] ?? 0;

            // Ne pas permettre la suppression de l'utilisateur connecté
            if ($userId == $_SESSION['user_id']) {
                echo json_encode(['success' => false, 'message' => 'Vous ne pouvez pas supprimer votre propre compte']);
                return;
            }

            if ($userModel->delete($userId)) {
                echo json_encode(['success' => true, 'message' => 'Utilisateur supprimé avec succès']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erreur lors de la suppression']);
            }
        }
    }

    public function assignEvaluator() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $articleId = $_POST['article_id'] ?? 0;
        $evaluatorId = $_POST['evaluator_id'] ?? 0;
        $deadline = $_POST['deadline'] ?? null;
        $instructions = $_POST['instructions'] ?? '';

        $evaluationModel = $this->loadModel('Evaluation');
        $evaluationData = [
            'article_id' => $articleId,
            'evaluator_id' => $evaluatorId,
            'status' => 'en_cours',
            'deadline' => $deadline,
            'instructions' => $instructions,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($evaluationModel->create($evaluationData)) {
            // Mettre à jour le statut de l'article
            $articleModel = $this->loadModel('Article');
            $articleModel->updateStatus($articleId, 'en_evaluation');
            
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false]);
        }
    }
    
    public function publishArticle() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $articleId = $_POST['article_id'] ?? 0;
        $volumeId = $_POST['volume_id'] ?? 0;
        $pageStart = $_POST['page_start'] ?? 0;
        $pageEnd = $_POST['page_end'] ?? 0;
        
        $articleModel = $this->loadModel('Article');
        $doi = $this->generateDOI($articleId);
        
        $updateData = [
            'status' => 'publie',
            'doi' => $doi,
            'volume_id' => $volumeId,
            'page_start' => $pageStart,
            'page_end' => $pageEnd,
            'published_at' => date('Y-m-d H:i:s')
        ];
        
        if ($articleModel->update($articleId, $updateData)) {
            echo json_encode(['success' => true, 'doi' => $doi]);
        } else {
            echo json_encode(['success' => false]);
        }
    }
    
    private function generateDOI($articleId) {
        return DOI_PREFIX . '.' . date('Y') . '.' . str_pad($articleId, 6, '0', STR_PAD_LEFT);
    }
    
    public function volumes() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $volumeModel = $this->loadModel('Volume');
        $volumes = $volumeModel->getAllWithArticleCount();
        
        $this->loadView('admin/volumes', ['volumes' => $volumes]);
    }
    
    public function createVolume() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $volumeModel = $this->loadModel('Volume');
            $volumeData = [
                'number' => $_POST['number'],
                'year' => $_POST['year'],
                'title' => $_POST['title'],
                'description' => $_POST['description'],
                'publication_date' => $_POST['publication_date'],
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            if ($volumeModel->create($volumeData)) {
                $this->redirect('admin/volumes?success=1');
            }
        }
        
        $this->loadView('admin/create_volume');
    }
}
?>