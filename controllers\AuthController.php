<?php
class AuthController extends Controller {
    
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userModel = $this->loadModel('User');
            $user = $userModel->findByEmail($_POST['email']);
            
            if ($user && $userModel->verifyPassword($_POST['password'], $user['password'])) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['role'] = $user['role'];
                $_SESSION['firstname'] = $user['firstname'];
                $_SESSION['lastname'] = $user['lastname'];
                
                $this->redirect('dashboard');
            } else {
                $error = "Email ou mot de passe incorrect";
                $this->loadView('auth/login', ['error' => $error]);
            }
        } else {
            $this->loadView('auth/login');
        }
    }
    
    public function register() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $userModel = $this->loadModel('User');
            
            // Validation
            $errors = [];
            if (empty($_POST['firstname'])) $errors[] = "Le prénom est requis";
            if (empty($_POST['lastname'])) $errors[] = "Le nom est requis";
            if (empty($_POST['email'])) $errors[] = "L'email est requis";
            if (empty($_POST['password'])) $errors[] = "Le mot de passe est requis";
            
            if ($userModel->findByEmail($_POST['email'])) {
                $errors[] = "Cet email est déjà utilisé";
            }
            
            if (empty($errors)) {
                $userData = [
                    'firstname' => $_POST['firstname'],
                    'lastname' => $_POST['lastname'],
                    'email' => $_POST['email'],
                    'password' => $_POST['password'],
                    'role' => $_POST['role'] ?? 'auteur',
                    'institution' => $_POST['institution'] ?? '',
                    'speciality' => $_POST['speciality'] ?? ''
                ];
                
                if ($userModel->createUser($userData)) {
                    $this->redirect('login?success=1');
                } else {
                    $errors[] = "Erreur lors de la création du compte";
                }
            }
            
            $this->loadView('auth/register', ['errors' => $errors]);
        } else {
            $this->loadView('auth/register');
        }
    }
    
    public function logout() {
        session_destroy();
        $this->redirect('');
    }
}
?>