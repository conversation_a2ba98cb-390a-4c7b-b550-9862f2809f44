<?php include 'views/layout/header.php'; ?>

<div class="hero-section gradient-primary text-white py-5 mb-5 position-relative overflow-hidden">
    <div class="container position-relative">
        <div class="row">
            <div class="col-lg-8 animate-slide-left">
                <h1 class="display-4 mb-4">
                    <i class="fas fa-graduation-cap me-3"></i>
                    Journal Scientifique Universitaire
                </h1>
                <p class="lead mb-4">
                    <i class="fas fa-microscope me-2"></i>
                    Revue scientifique multidisciplinaire dédiée à la recherche universitaire
                </p>
                <div class="d-flex gap-3">
                    <a href="<?= BASE_URL ?>articles" class="btn btn-light btn-lg btn-hover">
                        <i class="fas fa-book-open me-2"></i>
                        Parcourir les articles
                    </a>
                    <a href="<?= BASE_URL ?>register" class="btn btn-outline-light btn-lg btn-hover">
                        <i class="fas fa-user-plus me-2"></i>
                        Rejoindre
                    </a>
                </div>
            </div>
            <div class="col-lg-4 animate-slide-right">
                <div class="hero-stats">
                    <div class="stat-item">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <h3 class="counter" data-target="150">0</h3>
                        <p>Articles publiés</p>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3 class="counter" data-target="75">0</h3>
                        <p>Chercheurs</p>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-university fa-2x mb-2"></i>
                        <h3 class="counter" data-target="25">0</h3>
                        <p>Universités</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Particules de fond -->
    <div class="hero-particles"></div>
</div>

<div class="row">
    <div class="col-lg-8">
        <h2>Articles récents</h2>
        <?php if (!empty($recentArticles)): ?>
            <?php foreach ($recentArticles as $article): ?>
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title"><?= htmlspecialchars($article['title']) ?></h5>
                        <p class="card-text"><?= substr(htmlspecialchars($article['abstract']), 0, 200) ?>...</p>
                        <p class="card-text">
                            <small class="text-muted">
                                Par <?= htmlspecialchars($article['firstname'] . ' ' . $article['lastname']) ?>
                                - <?= date('d/m/Y', strtotime($article['published_at'])) ?>
                            </small>
                        </p>
                        <a href="<?= BASE_URL ?>articles/view?id=<?= $article['id'] ?>" class="btn btn-primary">Lire l'article</a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>Aucun article publié pour le moment.</p>
        <?php endif; ?>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5>Recherche</h5>
            </div>
            <div class="card-body">
                <form action="<?= BASE_URL ?>search" method="GET">
                    <div class="mb-3">
                        <input type="text" class="form-control" name="q" placeholder="Mots-clés...">
                    </div>
                    <div class="mb-3">
                        <select class="form-select" name="domain">
                            <option value="">Tous les domaines</option>
                            <?php foreach ($domains as $domain): ?>
                                <option value="<?= $domain['id'] ?>"><?= htmlspecialchars($domain['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Rechercher</button>
                </form>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>Domaines</h5>
            </div>
            <div class="card-body">
                <?php foreach ($domains as $domain): ?>
                    <span class="badge bg-secondary me-2 mb-2"><?= htmlspecialchars($domain['name']) ?></span>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<style>
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 50%, #004085 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    text-align: center;
}

.stat-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-item h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0.5rem 0;
    color: #fff;
}

.stat-item p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: none;
    font-weight: 600;
}

.badge {
    transition: all 0.3s ease;
    cursor: pointer;
}

.badge:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

@media (max-width: 768px) {
    .hero-stats {
        flex-direction: row;
        justify-content: space-around;
        margin-top: 2rem;
    }

    .stat-item {
        padding: 1rem;
        flex: 1;
        margin: 0 0.5rem;
    }

    .stat-item h3 {
        font-size: 1.8rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Créer des particules pour le hero
    createHeroParticles();

    // Animer les compteurs
    animateCounters();

    // Ajouter des animations aux cartes
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 + index * 100);
    });
});

function createHeroParticles() {
    const container = document.querySelector('.hero-particles');
    if (!container) return;

    for (let i = 0; i < 30; i++) {
        const particle = document.createElement('div');
        particle.style.cssText = `
            position: absolute;
            width: ${Math.random() * 4 + 2}px;
            height: ${Math.random() * 4 + 2}px;
            background: rgba(255, 255, 255, ${Math.random() * 0.5 + 0.2});
            border-radius: 50%;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: heroFloat ${Math.random() * 10 + 10}s infinite linear;
            animation-delay: ${Math.random() * 5}s;
        `;
        container.appendChild(particle);
    }
}

function animateCounters() {
    const counters = document.querySelectorAll('.counter');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000;
        const increment = target / (duration / 16);
        let current = 0;

        const updateCounter = () => {
            current += increment;
            if (current < target) {
                counter.textContent = Math.floor(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };

        // Démarrer l'animation après un délai
        setTimeout(updateCounter, 500);
    });
}
</script>

<style>
@keyframes heroFloat {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}
</style>

<?php include 'views/layout/footer.php'; ?>