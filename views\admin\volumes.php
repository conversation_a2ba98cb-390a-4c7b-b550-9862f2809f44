<?php include BASE_PATH . '/views/layout/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-book"></i> Gestion des Volumes</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createVolumeModal">
                    <i class="fas fa-plus"></i> Nouveau Volume
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <?php foreach ($volumes as $volume): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card volume-card h-100 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-book-open"></i> 
                        Volume <?= $volume['number'] ?> - <?= $volume['year'] ?>
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="card-title"><?= htmlspecialchars($volume['title']) ?></h6>
                    <p class="card-text text-muted"><?= htmlspecialchars($volume['description']) ?></p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="badge bg-info"><?= $volume['article_count'] ?> articles</span>
                        <span class="text-muted"><?= date('d/m/Y', strtotime($volume['created_at'])) ?></span>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Modifier
                        </button>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<script>
// Animation des cartes
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.volume-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'scale(0.9)';
        setTimeout(() => {
            card.style.transition = 'all 0.4s ease';
            card.style.opacity = '1';
            card.style.transform = 'scale(1)';
        }, index * 150);
    });
});
</script>

<?php include BASE_PATH . '/views/layout/footer.php'; ?>