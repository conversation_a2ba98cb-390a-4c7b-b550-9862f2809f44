-- Base de données pour le journal scientifique universitaire
CREATE DATABASE IF NOT EXISTS journal_scientifique;
USE journal_scientifique;

-- Table des utilisateurs
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    firstname <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    lastname <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('auteur', 'evaluateur', 'editeur') DEFAULT 'auteur',
    institution VARCHAR(255),
    speciality VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des domaines scientifiques
CREATE TABLE domains (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des articles
CREATE TABLE articles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(500) NOT NULL,
    abstract TEXT NOT NULL,
    keywords TEXT,
    domain_id INT,
    author_id INT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    status ENUM('soumis', 'en_evaluation', 'accepte', 'publie', 'rejete') DEFAULT 'soumis',
    doi VARCHAR(255),
    volume_id INT,
    page_start INT,
    page_end INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    published_at TIMESTAMP NULL,
    FOREIGN KEY (author_id) REFERENCES users(id),
    FOREIGN KEY (domain_id) REFERENCES domains(id)
);

-- Table des évaluations
CREATE TABLE evaluations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    article_id INT NOT NULL,
    evaluator_id INT NOT NULL,
    status ENUM('en_cours', 'terminee') DEFAULT 'en_cours',
    recommendation ENUM('accepter', 'accepter_avec_modifications', 'rejeter') NULL,
    comments TEXT,
    confidential_comments TEXT,
    evaluation_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles(id),
    FOREIGN KEY (evaluator_id) REFERENCES users(id)
);

-- Table des volumes
CREATE TABLE volumes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    number INT NOT NULL,
    year YEAR NOT NULL,
    title VARCHAR(255),
    description TEXT,
    publication_date DATE,
    is_published BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_volume (number, year)
);

-- Table des messages (messagerie interne)
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    article_id INT NOT NULL,
    sender_id INT NOT NULL,
    recipient_id INT NOT NULL,
    subject VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles(id),
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (recipient_id) REFERENCES users(id)
);

-- Table des fichiers de révision
CREATE TABLE revision_files (
    id INT PRIMARY KEY AUTO_INCREMENT,
    article_id INT NOT NULL,
    version INT NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    uploaded_by INT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (article_id) REFERENCES articles(id),
    FOREIGN KEY (uploaded_by) REFERENCES users(id)
);

-- Table de configuration du site
CREATE TABLE site_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des logs d'activité
CREATE TABLE activity_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(255) NOT NULL,
    entity_type VARCHAR(100),
    entity_id INT,
    details TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Insertion des domaines par défaut
INSERT INTO domains (name, description) VALUES
('Informatique', 'Sciences informatiques et technologies'),
('Mathématiques', 'Mathématiques pures et appliquées'),
('Physique', 'Physique théorique et expérimentale'),
('Chimie', 'Chimie organique, inorganique et analytique'),
('Biologie', 'Sciences biologiques et biotechnologies'),
('Médecine', 'Sciences médicales et santé'),
('Ingénierie', 'Sciences de l\'ingénieur'),
('Sciences Sociales', 'Sociologie, psychologie, anthropologie');

-- Insertion d'un utilisateur éditeur par défaut
INSERT INTO users (firstname, lastname, email, password, role, institution) VALUES
('Admin', 'Editeur', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'editeur', 'Université');

-- Insertion d'un évaluateur de test
INSERT INTO users (firstname, lastname, email, password, role, institution) VALUES
('Jean', 'Dupont', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'evaluateur', 'Université de Paris');

-- Insertion de volumes de test
INSERT INTO volumes (number, year, title, description, publication_date, created_at) VALUES
(1, 2024, 'Volume 1 - Sciences et Technologies', 'Premier volume de la revue scientifique', '2024-01-15', NOW()),
(2, 2024, 'Volume 2 - Recherche Interdisciplinaire', 'Deuxième volume axé sur la recherche interdisciplinaire', '2024-06-15', NOW());

-- Configuration du site par défaut
INSERT INTO site_config (config_key, config_value, description) VALUES
('site_title', 'Journal Scientifique Universitaire', 'Titre du site'),
('site_description', 'Revue scientifique universitaire multidisciplinaire', 'Description du site'),
('contact_email', '<EMAIL>', 'Email de contact'),
('submission_guidelines', 'Les articles doivent être soumis au format PDF...', 'Directives de soumission'),
('peer_review_policy', 'Tous les articles sont soumis à une évaluation par les pairs...', 'Politique d\'évaluation'),
('current_volume', '1', 'Volume actuel'),
('current_year', '2024', 'Année actuelle');

-- Index pour optimiser les performances
CREATE INDEX idx_articles_status ON articles(status);
CREATE INDEX idx_articles_author ON articles(author_id);
CREATE INDEX idx_articles_domain ON articles(domain_id);
CREATE INDEX idx_evaluations_article ON evaluations(article_id);
CREATE INDEX idx_evaluations_evaluator ON evaluations(evaluator_id);
CREATE INDEX idx_messages_article ON messages(article_id);
CREATE INDEX idx_messages_recipient ON messages(recipient_id);