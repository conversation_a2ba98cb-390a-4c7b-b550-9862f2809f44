// filepath: web-project/src/app.js
document.addEventListener('DOMContentLoaded', () => {
    const images = [
        'images/image1.jpeg',
        'images/image2.jpeg',
        'images/image3.png',
        'images/image4.jpeg',
        'images/image5.jpg'
    ];

    let currentIndex = 0;
    const backgroundElement = document.body;

    function changeBackground() {
        backgroundElement.style.backgroundImage = `url('${images[currentIndex]}')`;
        backgroundElement.style.transition = 'background-image 1s ease-in-out';
        currentIndex = (currentIndex + 1) % images.length;
    }

    setInterval(changeBackground, 5000); // Change background every 5 seconds

    // Initial background setup
    changeBackground();
});