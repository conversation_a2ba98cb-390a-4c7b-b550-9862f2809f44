/* Animations et effets visuels pour le journal scientifique */

/* Bulles flottantes */
@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.7;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(30px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInScale {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

/* Classes d'animation */
.floating-bubble {
    position: fixed;
    border-radius: 50%;
    pointer-events: none;
    z-index: -1;
    animation: float linear infinite;
}

.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-fade-scale {
    animation: fadeInScale 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Effets de hover améliorés */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 15px;
    overflow: hidden;
}

.card-hover:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.btn-hover {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.btn-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-hover:hover::before {
    left: 100%;
}

/* Gradients modernes */
.gradient-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.gradient-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.gradient-info {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
}

.gradient-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.gradient-danger {
    background: linear-gradient(135deg, #dc3545, #bd2130);
}

/* Effets de focus améliorés */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
    transition: all 0.3s ease;
}

/* Modals avec animations */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    animation: fadeInScale 0.3s ease-out;
}

.modal-header {
    border-radius: 15px 15px 0 0;
}

/* Alertes animées */
.alert {
    border-radius: 10px;
    border: none;
    animation: slideInUp 0.5s ease-out;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

/* Tables avec animations */
.table-hover tbody tr {
    transition: all 0.3s ease;
}

.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.1) !important;
    transform: scale(1.01);
}

/* Badges animés */
.badge {
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* Effets de chargement */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive animations */
@media (max-width: 768px) {
    .card-hover:hover {
        transform: translateY(-5px) scale(1.01);
    }
    
    .floating-bubble {
        display: none; /* Masquer les bulles sur mobile pour les performances */
    }
}

/* Effets de particules pour les pages importantes */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(0,123,255,0.3);
    border-radius: 50%;
    animation: particleFloat 15s infinite linear;
}

@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) translateX(100px);
        opacity: 0;
    }
}
