<?php
class ArticleController extends Controller {
    
    public function index() {
        $this->requireLogin();
        $articleModel = $this->loadModel('Article');
        
        if ($this->hasRole('auteur')) {
            $articles = $articleModel->getByAuthor($_SESSION['user_id']);
        } else {
            $articles = $articleModel->findAll();
        }
        
        $this->loadView('articles/index', ['articles' => $articles]);
    }
    
    public function submit() {
        $this->requireLogin();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $errors = [];
            
            // Validation
            if (empty($_POST['title'])) $errors[] = "Le titre est requis";
            if (empty($_POST['abstract'])) $errors[] = "Le résumé est requis";
            if (empty($_FILES['pdf']['name'])) $errors[] = "Le fichier PDF est requis";
            
            // Validation fichier
            if (!empty($_FILES['pdf']['name'])) {
                $fileExtension = strtolower(pathinfo($_FILES['pdf']['name'], PATHINFO_EXTENSION));
                if (!in_array($fileExtension, ALLOWED_EXTENSIONS)) {
                    $errors[] = "Seuls les fichiers PDF sont autorisés";
                }
                if ($_FILES['pdf']['size'] > MAX_FILE_SIZE) {
                    $errors[] = "Le fichier est trop volumineux";
                }
            }
            
            if (empty($errors)) {
                // Upload du fichier
                $fileName = uniqid() . '.pdf';
                $uploadPath = UPLOAD_PATH . 'articles/' . $fileName;
                
                if (move_uploaded_file($_FILES['pdf']['tmp_name'], $uploadPath)) {
                    $articleModel = $this->loadModel('Article');
                    $articleData = [
                        'title' => $_POST['title'],
                        'abstract' => $_POST['abstract'],
                        'keywords' => $_POST['keywords'],
                        'domain' => $_POST['domain'],
                        'author_id' => $_SESSION['user_id'],
                        'file_path' => $uploadPath,
                        'status' => STATUS_SUBMITTED,
                        'created_at' => date('Y-m-d H:i:s')
                    ];
                    
                    if ($articleModel->create($articleData)) {
                        $this->redirect('articles?success=1');
                    } else {
                        $errors[] = "Erreur lors de la soumission";
                    }
                } else {
                    $errors[] = "Erreur lors de l'upload du fichier";
                }
            }
            
            $this->loadView('articles/submit', ['errors' => $errors]);
        } else {
            $this->loadView('articles/submit');
        }
    }
    
    public function view() {
        $this->requireLogin();
        $id = $_GET['id'] ?? 0;
        
        $articleModel = $this->loadModel('Article');
        $article = $articleModel->findById($id);
        
        if (!$article) {
            $this->redirect('articles');
        }
        
        $this->loadView('articles/view', ['article' => $article]);
    }
}
?>