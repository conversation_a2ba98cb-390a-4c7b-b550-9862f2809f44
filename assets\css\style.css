/* Styles personnalisés pour le journal scientifique */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
}

.status-soumis { background-color: #17a2b8; }
.status-en_evaluation { background-color: #ffc107; color: #212529; }
.status-accepte { background-color: #28a745; }
.status-publie { background-color: #007bff; }
.status-rejete { background-color: #dc3545; }

.article-card {
    transition: transform 0.2s;
}

.article-card:hover {
    transform: translateY(-2px);
}

.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 0.375rem;
    padding: 2rem;
    text-align: center;
    transition: border-color 0.15s ease-in-out;
}

.file-upload-area:hover {
    border-color: #007bff;
}

.evaluation-form {
    background-color: #fff;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.message-thread {
    max-height: 400px;
    overflow-y: auto;
}

.message-item {
    border-left: 3px solid #007bff;
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.anonymous-message {
    border-left-color: #6c757d;
}

footer {
    margin-top: auto;
}

.dashboard-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 0.5rem;
    padding: 1.5rem;
}

.search-form {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.375rem;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}