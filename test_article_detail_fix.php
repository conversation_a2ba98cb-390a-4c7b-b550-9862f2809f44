<?php
require_once 'init.php';

echo "<h1>Test de correction de la vue d'article</h1>";

// Simuler une session utilisateur pour les tests
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'auteur';
    $_SESSION['firstname'] = 'Test';
    $_SESSION['lastname'] = 'User';
}

$articleModel = new Article();
$articles = $articleModel->findAll();

if (empty($articles)) {
    echo "<div style='color: red;'>❌ Aucun article trouvé. Créez d'abord des données de test.</div>";
    echo "<p><a href='check_and_fix.php'>Créer des données de test</a></p>";
} else {
    echo "<h2>Articles disponibles pour test :</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    foreach ($articles as $article) {
        // Tester la nouvelle méthode
        $articleWithDetails = $articleModel->findByIdWithDetails($article['id']);
        
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>" . htmlspecialchars($article['title']) . "</h3>";
        
        if ($articleWithDetails && isset($articleWithDetails['firstname']) && isset($articleWithDetails['lastname'])) {
            echo "<p style='color: green;'>✅ <strong>Auteur :</strong> " . htmlspecialchars($articleWithDetails['firstname'] . ' ' . $articleWithDetails['lastname']) . "</p>";
            
            if (!empty($articleWithDetails['institution'])) {
                echo "<p><strong>Institution :</strong> " . htmlspecialchars($articleWithDetails['institution']) . "</p>";
            }
            
            echo "<p><strong>Statut :</strong> " . htmlspecialchars($articleWithDetails['status']) . "</p>";
            echo "<p><strong>Date :</strong> " . date('d/m/Y', strtotime($articleWithDetails['created_at'])) . "</p>";
            
            // Lien pour tester la vraie vue
            echo "<a href='" . BASE_URL . "articles/view?id=" . $article['id'] . "' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Voir les détails</a>";
            
        } else {
            echo "<p style='color: red;'>❌ Erreur : Impossible de récupérer les détails de l'auteur</p>";
        }
        
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Instructions :</h2>";
    echo "<ol>";
    echo "<li>Cliquez sur 'Voir les détails' pour un article</li>";
    echo "<li>Vérifiez que le nom de l'auteur s'affiche correctement (sans erreurs PHP)</li>";
    echo "<li>Vérifiez que toutes les informations de l'auteur sont visibles dans la sidebar</li>";
    echo "</ol>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
a { color: #007bff; }
a:hover { color: #0056b3; }
</style>
