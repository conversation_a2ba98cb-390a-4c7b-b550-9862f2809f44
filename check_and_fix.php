<?php
require_once 'init.php';

echo "<h1>Vérification et correction de la base de données</h1>";

try {
    $db = Database::getInstance()->getConnection();
    echo "<p style='color: green;'>✓ Connexion à la base de données réussie</p>";
    
    // Vérifier et créer un évaluateur de test
    $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE role = 'evaluateur'");
    $stmt->execute();
    $evaluatorCount = $stmt->fetchColumn();
    
    if ($evaluatorCount == 0) {
        $stmt = $db->prepare("INSERT INTO users (firstname, lastname, email, password, role, institution) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['Jean', 'Dupont', '<EMAIL>', password_hash('password', PASSWORD_DEFAULT), 'evaluateur', 'Université de Paris']);
        echo "<p style='color: blue;'>✓ Évaluateur de test créé</p>";
    } else {
        echo "<p>✓ $evaluatorCount évaluateur(s) trouvé(s)</p>";
    }
    
    // Vérifier et créer des volumes de test
    $stmt = $db->prepare("SELECT COUNT(*) FROM volumes");
    $stmt->execute();
    $volumeCount = $stmt->fetchColumn();
    
    if ($volumeCount == 0) {
        $stmt = $db->prepare("INSERT INTO volumes (number, year, title, description, publication_date, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
        $stmt->execute([1, 2024, 'Volume 1 - Sciences et Technologies', 'Premier volume de la revue scientifique', '2024-01-15']);
        $stmt->execute([2, 2024, 'Volume 2 - Recherche Interdisciplinaire', 'Deuxième volume axé sur la recherche interdisciplinaire', '2024-06-15']);
        echo "<p style='color: blue;'>✓ Volumes de test créés</p>";
    } else {
        echo "<p>✓ $volumeCount volume(s) trouvé(s)</p>";
    }
    
    // Vérifier et créer des articles de test
    $stmt = $db->prepare("SELECT COUNT(*) FROM articles WHERE status = 'soumis'");
    $stmt->execute();
    $submittedCount = $stmt->fetchColumn();
    
    $stmt = $db->prepare("SELECT COUNT(*) FROM articles WHERE status = 'accepte'");
    $stmt->execute();
    $acceptedCount = $stmt->fetchColumn();
    
    // Récupérer l'ID de l'éditeur
    $stmt = $db->prepare("SELECT id FROM users WHERE role = 'editeur' LIMIT 1");
    $stmt->execute();
    $editorId = $stmt->fetchColumn();
    
    if ($editorId) {
        if ($submittedCount == 0) {
            $stmt = $db->prepare("INSERT INTO articles (title, abstract, keywords, domain_id, author_id, file_path, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                'Analyse des Données et Big Data',
                'Cette recherche présente une analyse approfondie des techniques modernes de traitement des big data.',
                'big data, analyse de données, statistiques',
                1, // Informatique
                $editorId,
                'uploads/articles/test_article_soumis.pdf',
                'soumis'
            ]);
            echo "<p style='color: blue;'>✓ Article de test créé avec statut 'soumis'</p>";
        } else {
            echo "<p>✓ $submittedCount article(s) avec statut 'soumis' trouvé(s)</p>";
        }
        
        if ($acceptedCount == 0) {
            $stmt = $db->prepare("INSERT INTO articles (title, abstract, keywords, domain_id, author_id, file_path, status, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([
                'Intelligence Artificielle et Machine Learning',
                'Cet article explore les dernières avancées en intelligence artificielle et machine learning.',
                'intelligence artificielle, machine learning, recherche',
                1, // Informatique
                $editorId,
                'uploads/articles/test_article_accepte.pdf',
                'accepte'
            ]);
            echo "<p style='color: blue;'>✓ Article de test créé avec statut 'accepté'</p>";
        } else {
            echo "<p>✓ $acceptedCount article(s) avec statut 'accepté' trouvé(s)</p>";
        }
    }
    
    // Afficher le résumé
    echo "<h2>Résumé de la base de données</h2>";
    
    $stmt = $db->prepare("SELECT status, COUNT(*) as count FROM articles GROUP BY status");
    $stmt->execute();
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th style='padding: 5px;'>Statut</th><th style='padding: 5px;'>Nombre d'articles</th></tr>";
    foreach ($statuses as $status) {
        echo "<tr><td style='padding: 5px;'>{$status['status']}</td><td style='padding: 5px;'>{$status['count']}</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>Actions de test</h2>";
    echo "<ol>";
    echo "<li><a href='http://localhost/journal-scientifique/admin/articles' target='_blank'>Aller à la page d'administration des articles</a></li>";
    echo "<li>Connectez-vous avec : <EMAIL> / password</li>";
    echo "<li>Vous devriez voir :</li>";
    echo "<ul>";
    echo "<li>Un article avec statut 'soumis' et un bouton 'Assigner' orange</li>";
    echo "<li>Un article avec statut 'accepté' et un bouton 'Publier' vert</li>";
    echo "</ul>";
    echo "<li>Testez les deux boutons pour vérifier qu'ils ouvrent les bonnes modales</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Erreur : " . $e->getMessage() . "</p>";
}
?>
