<?php
require_once 'init.php';

echo "<h1>Test de modification d'articles</h1>";

// Simuler une session utilisateur pour les tests
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'auteur';
    $_SESSION['firstname'] = 'Test';
    $_SESSION['lastname'] = 'User';
}

$articleModel = new Article();
$articles = $articleModel->getByAuthor($_SESSION['user_id']);

if (empty($articles)) {
    echo "<div style='color: red;'>❌ Aucun article trouvé pour cet auteur.</div>";
    echo "<p><a href='check_and_fix.php'>Créer des données de test</a></p>";
} else {
    echo "<h2>Articles de l'auteur (ID: {$_SESSION['user_id']}) :</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    foreach ($articles as $article) {
        echo "<div style='border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h3>" . htmlspecialchars($article['title']) . "</h3>";
        echo "<p><strong>Statut :</strong> " . htmlspecialchars($article['status']) . "</p>";
        echo "<p><strong>Date :</strong> " . date('d/m/Y', strtotime($article['created_at'])) . "</p>";
        
        if ($article['status'] === 'soumis') {
            echo "<p style='color: green;'>✅ <strong>Peut être modifié</strong></p>";
            echo "<a href='" . BASE_URL . "articles/edit?id=" . $article['id'] . "' target='_blank' style='background: #ffc107; color: #000; padding: 8px 15px; text-decoration: none; border-radius: 3px; margin-right: 10px;'>
                    <i class='fas fa-edit'></i> Modifier
                  </a>";
        } else {
            echo "<p style='color: orange;'>⚠️ <strong>Ne peut pas être modifié</strong> (statut: " . htmlspecialchars($article['status']) . ")</p>";
        }
        
        echo "<a href='" . BASE_URL . "articles/view?id=" . $article['id'] . "' target='_blank' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>
                <i class='fas fa-eye'></i> Voir
              </a>";
        
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>Test de la liste des articles :</h2>";
    echo "<a href='" . BASE_URL . "articles' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0;'>
            <i class='fas fa-list'></i> Voir la liste des articles
          </a>";
    
    echo "<h2>Instructions de test :</h2>";
    echo "<ol>";
    echo "<li>Cliquez sur 'Modifier' pour un article avec le statut 'soumis'</li>";
    echo "<li>Vérifiez que le formulaire est pré-rempli avec les données existantes</li>";
    echo "<li>Modifiez le titre ou le résumé</li>";
    echo "<li>Optionnellement, uploadez un nouveau fichier PDF</li>";
    echo "<li>Cliquez sur 'Enregistrer les modifications'</li>";
    echo "<li>Vérifiez que vous êtes redirigé vers la liste avec un message de succès</li>";
    echo "</ol>";
    
    echo "<h2>Vérifications de sécurité :</h2>";
    echo "<ul>";
    echo "<li>✅ Seuls les articles avec le statut 'soumis' peuvent être modifiés</li>";
    echo "<li>✅ Seul l'auteur de l'article peut le modifier</li>";
    echo "<li>✅ Le fichier PDF est optionnel lors de la modification</li>";
    echo "<li>✅ L'ancien fichier est supprimé si un nouveau est uploadé</li>";
    echo "</ul>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
a { color: #007bff; }
a:hover { opacity: 0.8; }
ol, ul { background: #f9f9f9; padding: 15px; border-radius: 5px; }
</style>
