<?php include BASE_PATH . '/views/layout/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> Gestion des Utilisateurs</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                    <i class="fas fa-user-plus"></i> Ajouter Utilisateur
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Nom</th>
                                    <th>Email</th>
                                    <th>Rôle</th>
                                    <th>Date d'inscription</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?= $user['id'] ?></td>
                                    <td><?= htmlspecialchars($user['firstname'] . ' ' . $user['lastname']) ?></td>
                                    <td><?= htmlspecialchars($user['email']) ?></td>
                                    <td>
                                        <span class="badge bg-<?= $user['role'] === 'editeur' ? 'danger' : ($user['role'] === 'evaluateur' ? 'warning' : 'primary') ?>">
                                            <?= ucfirst($user['role']) ?>
                                        </span>
                                    </td>
                                    <td><?= date('d/m/Y', strtotime($user['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-warning edit-user-btn" data-id="<?= $user['id'] ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger delete-user-btn" data-id="<?= $user['id'] ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un utilisateur -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">Ajouter un Utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_firstname" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="add_firstname" name="firstname" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_lastname" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="add_lastname" name="lastname" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="add_email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_password" class="form-label">Mot de passe *</label>
                                <input type="password" class="form-control" id="add_password" name="password" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_role" class="form-label">Rôle</label>
                                <select class="form-select" id="add_role" name="role" required>
                                    <option value="auteur">Auteur</option>
                                    <option value="evaluateur">Évaluateur</option>
                                    <option value="editeur">Éditeur</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="add_institution" class="form-label">Institution</label>
                                <input type="text" class="form-control" id="add_institution" name="institution">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="add_speciality" class="form-label">Spécialité</label>
                        <input type="text" class="form-control" id="add_speciality" name="speciality">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmAddUser">Ajouter</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour modifier un utilisateur -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">Modifier un Utilisateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="edit_user_id" name="user_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_firstname" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="edit_firstname" name="firstname" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_lastname" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="edit_lastname" name="lastname" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="edit_email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_password" class="form-label">Nouveau mot de passe (optionnel)</label>
                                <input type="password" class="form-control" id="edit_password" name="password">
                                <small class="form-text text-muted">Laissez vide pour conserver le mot de passe actuel</small>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_role" class="form-label">Rôle</label>
                                <select class="form-select" id="edit_role" name="role" required>
                                    <option value="auteur">Auteur</option>
                                    <option value="evaluateur">Évaluateur</option>
                                    <option value="editeur">Éditeur</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_institution" class="form-label">Institution</label>
                                <input type="text" class="form-control" id="edit_institution" name="institution">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_speciality" class="form-label">Spécialité</label>
                        <input type="text" class="form-control" id="edit_speciality" name="speciality">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-warning" id="confirmEditUser">Modifier</button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Gestion des utilisateurs initialisée');

    // Gestionnaire pour ajouter un utilisateur
    document.getElementById('confirmAddUser').addEventListener('click', function() {
        const form = document.getElementById('addUserForm');
        const formData = new FormData(form);

        fetch('<?= BASE_URL ?>admin/createUser', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Utilisateur ajouté avec succès !');
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'ajout de l\'utilisateur');
        });
    });

    // Gestionnaires pour les boutons de modification
    document.querySelectorAll('.edit-user-btn').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');

            // Récupérer les données de l'utilisateur depuis la ligne du tableau
            const row = this.closest('tr');
            const cells = row.querySelectorAll('td');

            const fullName = cells[1].textContent.trim().split(' ');
            const firstname = fullName[0] || '';
            const lastname = fullName.slice(1).join(' ') || '';
            const email = cells[2].textContent.trim();
            const role = cells[3].querySelector('.badge').textContent.toLowerCase().trim();

            // Remplir le formulaire de modification
            document.getElementById('edit_user_id').value = userId;
            document.getElementById('edit_firstname').value = firstname;
            document.getElementById('edit_lastname').value = lastname;
            document.getElementById('edit_email').value = email;
            document.getElementById('edit_role').value = role;

            // Ouvrir la modal
            const modal = new bootstrap.Modal(document.getElementById('editUserModal'));
            modal.show();
        });
    });

    // Gestionnaire pour modifier un utilisateur
    document.getElementById('confirmEditUser').addEventListener('click', function() {
        const form = document.getElementById('editUserForm');
        const formData = new FormData(form);

        fetch('<?= BASE_URL ?>admin/updateUser', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Utilisateur modifié avec succès !');
                location.reload();
            } else {
                alert('Erreur : ' + data.message);
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la modification de l\'utilisateur');
        });
    });

    // Gestionnaires pour les boutons de suppression
    document.querySelectorAll('.delete-user-btn').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-id');
            const row = this.closest('tr');
            const userName = row.querySelector('td:nth-child(2)').textContent.trim();

            if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${userName}" ?\n\nCette action est irréversible.`)) {
                const formData = new FormData();
                formData.append('user_id', userId);

                fetch('<?= BASE_URL ?>admin/deleteUser', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Utilisateur supprimé avec succès !');
                        location.reload();
                    } else {
                        alert('Erreur : ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Erreur lors de la suppression de l\'utilisateur');
                });
            }
        });
    });
});
</script>

<?php include BASE_PATH . '/views/layout/footer.php'; ?>