<?php
class EvaluationController extends Controller {
    
    public function index() {
        $this->requireLogin();
        
        if (!$this->hasRole('evaluateur')) {
            $this->redirect('dashboard');
        }
        
        // Logique pour les évaluations
        $data = [
            'pendingEvaluations' => [],
            'completedEvaluations' => []
        ];
        
        $this->loadView('evaluation/index', $data);
    }
}
?>
