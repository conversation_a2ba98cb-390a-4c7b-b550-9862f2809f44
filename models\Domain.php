<?php
class Domain extends Model {
    protected $table = 'domains';
    
    public function getWithArticleCount() {
        $stmt = $this->db->prepare("
            SELECT d.*, COUNT(a.id) as article_count 
            FROM domains d 
            LEFT JOIN articles a ON d.id = a.domain_id 
            GROUP BY d.id 
            ORDER BY d.name
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>