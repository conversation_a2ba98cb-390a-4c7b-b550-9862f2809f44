// Initialisation immédiate
document.addEventListener('DOMContentLoaded', () => {
    // <PERSON><PERSON>er les bulles immédiatement
    createSimpleBubbles();
    
    // Démarrer l'animation système
    new AnimationSystem();
});

// Fonction simple pour créer les bulles
function createSimpleBubbles() {
    const container = document.createElement('div');
    container.className = 'particles-container';
    container.style.cssText = `
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        pointer-events: none !important;
        z-index: 999 !important;
        overflow: hidden !important;
    `;
    
    // Créer 20 bulles simples
    for (let i = 0; i < 20; i++) {
        const bubble = document.createElement('div');
        bubble.className = 'particle';
        
        const size = Math.random() * 40 + 20;
        const animationDelay = Math.random() * 10;
        const animationDuration = Math.random() * 15 + 20;
        
        bubble.style.cssText = `
            position: absolute !important;
            width: ${size}px !important;
            height: ${size}px !important;
            background: rgba(255, 255, 255, 0.3) !important;
            border-radius: 50% !important;
            backdrop-filter: blur(3px) !important;
            border: 2px solid rgba(255, 255, 255, 0.5) !important;
            box-shadow: 0 4px 20px rgba(255, 255, 255, 0.3) !important;
            left: ${Math.random() * 100}% !important;
            animation: floatBubble ${animationDuration}s infinite linear !important;
            animation-delay: ${animationDelay}s !important;
        `;
        
        container.appendChild(bubble);
    }
    
    document.body.appendChild(container);
}

// Système d'animation simplifié
class AnimationSystem {
    constructor() {
        this.images = [
            'assets/images/image1.jpeg',
            'assets/images/image2.jpeg', 
            'assets/images/image3.jpeg',
            'assets/images/image4.jpeg',
            'assets/images/image5.jpg'
        ];
        this.currentImageIndex = 0;
        this.init();
    }

    init() {
        this.createBackground();
        this.startSmoothBackgroundRotation();
    }

    createBackground() {
        // Créer un overlay pour transition douce
        const overlay = document.createElement('div');
        overlay.className = 'background-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0;
            transition: opacity 3s ease-in-out;
        `;
        document.body.appendChild(overlay);
        
        // Image de fond initiale
        document.body.style.backgroundImage = `url('${this.images[0]}')`;
        document.body.style.backgroundSize = "cover";
        document.body.style.backgroundPosition = "center";
        document.body.style.backgroundAttachment = "fixed";
    }

    startSmoothBackgroundRotation() {
        setInterval(() => {
            const overlay = document.querySelector('.background-overlay');
            const nextIndex = (this.currentImageIndex + 1) % this.images.length;
            
            // Préparer la nouvelle image dans l'overlay
            overlay.style.backgroundImage = `url('${this.images[nextIndex]}')`;
            overlay.style.backgroundSize = "cover";
            overlay.style.backgroundPosition = "center";
            overlay.style.backgroundAttachment = "fixed";
            
            // Transition douce
            overlay.style.opacity = '1';
            
            setTimeout(() => {
                // Appliquer la nouvelle image au body
                document.body.style.backgroundImage = `url('${this.images[nextIndex]}')`;
                overlay.style.opacity = '0';
                this.currentImageIndex = nextIndex;
            }, 3000);
            
        }, 8000); // Changer toutes les 8 secondes
    }
}

// Effet de clic sur les boutons
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('btn')) {
        const ripple = document.createElement('span');
        const rect = e.target.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        e.target.appendChild(ripple);
        
        setTimeout(() => ripple.remove(), 600);
    }
});





