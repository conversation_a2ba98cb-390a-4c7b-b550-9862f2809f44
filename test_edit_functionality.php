<?php
require_once 'init.php';

echo "<h1>Test complet de la fonctionnalité de modification d'articles</h1>";

// Simuler une session utilisateur
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['role'] = 'auteur';
    $_SESSION['firstname'] = 'Test';
    $_SESSION['lastname'] = 'User';
}

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>✅ Fonctionnalité de modification d'articles implémentée !</h2>";
echo "<p><strong>Nouvelles fonctionnalités ajoutées :</strong></p>";
echo "<ul>";
echo "<li>✅ Route <code>articles/edit</code> ajoutée</li>";
echo "<li>✅ Méthode <code>edit()</code> dans ArticleController</li>";
echo "<li>✅ Vue <code>views/articles/edit.php</code> créée</li>";
echo "<li>✅ Boutons de modification dans la liste des articles</li>";
echo "<li>✅ Messages de succès et d'erreur</li>";
echo "<li>✅ Validation de sécurité (seul l'auteur peut modifier)</li>";
echo "<li>✅ Restriction aux articles avec statut 'soumis'</li>";
echo "</ul>";
echo "</div>";

// Vérifier les articles existants
$articleModel = new Article();
$articles = $articleModel->getByAuthor($_SESSION['user_id']);

echo "<h2>Articles de test disponibles :</h2>";

if (empty($articles)) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>⚠️ Aucun article trouvé pour l'utilisateur ID: {$_SESSION['user_id']}</p>";
    echo "<p><a href='check_and_fix.php' style='background: #007bff; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>Créer des données de test</a></p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ " . count($articles) . " article(s) trouvé(s)</p>";
    
    foreach ($articles as $article) {
        echo "<div style='border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 3px; background: white;'>";
        echo "<h4>" . htmlspecialchars($article['title']) . "</h4>";
        echo "<p><strong>Statut :</strong> " . htmlspecialchars($article['status']);
        
        if ($article['status'] === 'soumis') {
            echo " <span style='color: green;'>✅ Modifiable</span>";
        } else {
            echo " <span style='color: orange;'>⚠️ Non modifiable</span>";
        }
        echo "</p>";
        
        echo "<div style='margin-top: 10px;'>";
        echo "<a href='" . BASE_URL . "articles/view?id=" . $article['id'] . "' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px; margin-right: 5px;'>Voir</a>";
        
        if ($article['status'] === 'soumis') {
            echo "<a href='" . BASE_URL . "articles/edit?id=" . $article['id'] . "' target='_blank' style='background: #ffc107; color: #000; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Modifier</a>";
        }
        echo "</div>";
        echo "</div>";
    }
    echo "</div>";
}

echo "<h2>Tests à effectuer :</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<ol>";
echo "<li><strong>Test de la liste :</strong> <a href='" . BASE_URL . "articles' target='_blank'>Aller à la liste des articles</a></li>";
echo "<li><strong>Vérifier les boutons :</strong> Les articles avec statut 'soumis' doivent avoir un bouton 'Modifier'</li>";
echo "<li><strong>Test de modification :</strong> Cliquer sur 'Modifier' pour un article</li>";
echo "<li><strong>Vérifier le formulaire :</strong> Le formulaire doit être pré-rempli</li>";
echo "<li><strong>Test de sauvegarde :</strong> Modifier des champs et sauvegarder</li>";
echo "<li><strong>Vérifier la redirection :</strong> Retour à la liste avec message de succès</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Sécurité implémentée :</h2>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
echo "<ul>";
echo "<li>✅ <strong>Authentification :</strong> Utilisateur doit être connecté</li>";
echo "<li>✅ <strong>Autorisation :</strong> Seul l'auteur peut modifier son article</li>";
echo "<li>✅ <strong>Statut :</strong> Seuls les articles 'soumis' peuvent être modifiés</li>";
echo "<li>✅ <strong>Validation :</strong> Titre et résumé obligatoires</li>";
echo "<li>✅ <strong>Fichiers :</strong> Validation PDF optionnelle</li>";
echo "<li>✅ <strong>Gestion fichiers :</strong> Suppression de l'ancien fichier si nouveau uploadé</li>";
echo "</ul>";
echo "</div>";

echo "<h2>Fonctionnalités :</h2>";
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
echo "<ul>";
echo "<li>📝 <strong>Modification des métadonnées :</strong> Titre, résumé, mots-clés, domaine</li>";
echo "<li>📄 <strong>Remplacement de fichier :</strong> Upload optionnel d'un nouveau PDF</li>";
echo "<li>👁️ <strong>Aperçu du fichier actuel :</strong> Lien pour voir le PDF existant</li>";
echo "<li>✅ <strong>Validation côté client :</strong> JavaScript pour validation immédiate</li>";
echo "<li>🔄 <strong>Messages de retour :</strong> Succès et erreurs</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center;'>";
echo "<h3 style='color: #155724; margin: 0;'>🎉 Problème résolu !</h3>";
echo "<p style='margin: 10px 0;'>Les auteurs peuvent maintenant modifier leurs articles soumis.</p>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
a { color: #007bff; }
a:hover { opacity: 0.8; }
</style>
