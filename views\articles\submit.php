<?php include 'views/layout/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-header">
                <h4>Soumettre un article</h4>
            </div>
            <div class="card-body">
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST" enctype="multipart/form-data" class="needs-validation auto-save" id="article-form" novalidate>
                    <div class="mb-3">
                        <label for="title" class="form-label">Titre de l'article *</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="abstract" class="form-label">Résumé *</label>
                        <textarea class="form-control" id="abstract" name="abstract" rows="6" required></textarea>
                        <div class="form-text">Maximum 500 mots</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="keywords" class="form-label">Mots-clés</label>
                        <input type="text" class="form-control" id="keywords" name="keywords">
                        <div class="form-text">Séparez les mots-clés par des virgules</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="domain" class="form-label">Domaine scientifique *</label>
                        <select class="form-select" id="domain" name="domain" required>
                            <option value="">Sélectionnez un domaine</option>
                            <option value="1">Informatique</option>
                            <option value="2">Mathématiques</option>
                            <option value="3">Physique</option>
                            <option value="4">Chimie</option>
                            <option value="5">Biologie</option>
                            <option value="6">Médecine</option>
                            <option value="7">Ingénierie</option>
                            <option value="8">Sciences Sociales</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="pdf" class="form-label">Fichier PDF de l'article *</label>
                        <div class="file-upload-area">
                            <input type="file" class="form-control" id="pdf" name="pdf" accept=".pdf" required>
                            <div class="mt-2">
                                <i class="fas fa-cloud-upload-alt fa-2x text-muted"></i>
                                <p class="text-muted">Glissez-déposez votre fichier PDF ici ou cliquez pour sélectionner</p>
                                <small class="text-muted">Taille maximale: 10MB</small>
                            </div>
                        </div>
                        <div class="file-info mt-2"></div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="terms" required>
                            <label class="form-check-label" for="terms">
                                Je certifie que cet article est original et n'a pas été publié ailleurs *
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= BASE_URL ?>articles" class="btn btn-secondary">Annuler</a>
                        <button type="submit" class="btn btn-primary">Soumettre l'article</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Charger les données sauvegardées
    const form = document.getElementById('article-form');
    loadFormData(form);
});
</script>

<?php include 'views/layout/footer.php'; ?>