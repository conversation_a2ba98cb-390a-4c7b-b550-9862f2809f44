<?php
require_once 'init.php';

echo "<h1>Diagnostic de l'application Journal Scientifique</h1>";

try {
    $db = Database::getInstance()->getConnection();
    echo "<p style='color: green;'>✓ Connexion à la base de données réussie</p>";
    
    // Vérifier les tables
    $tables = ['users', 'articles', 'volumes', 'domains', 'evaluations'];
    foreach ($tables as $table) {
        $stmt = $db->prepare("SELECT COUNT(*) FROM $table");
        $stmt->execute();
        $count = $stmt->fetchColumn();
        echo "<p>Table '$table': $count enregistrements</p>";
    }
    
    // Vérifier les utilisateurs par rôle
    echo "<h2>Utilisateurs par rôle</h2>";
    $stmt = $db->prepare("SELECT role, COUNT(*) as count FROM users GROUP BY role");
    $stmt->execute();
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($roles as $role) {
        echo "<p>Rôle '{$role['role']}': {$role['count']} utilisateurs</p>";
    }
    
    // Vérifier les articles par statut
    echo "<h2>Articles par statut</h2>";
    $stmt = $db->prepare("SELECT status, COUNT(*) as count FROM articles GROUP BY status");
    $stmt->execute();
    $statuses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($statuses as $status) {
        echo "<p>Statut '{$status['status']}': {$status['count']} articles</p>";
    }
    
    // Vérifier les volumes
    echo "<h2>Volumes disponibles</h2>";
    $stmt = $db->prepare("SELECT * FROM volumes ORDER BY year DESC, number DESC");
    $stmt->execute();
    $volumes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (empty($volumes)) {
        echo "<p style='color: red;'>⚠ Aucun volume trouvé</p>";
    } else {
        foreach ($volumes as $volume) {
            echo "<p>Volume {$volume['number']} - {$volume['year']}: {$volume['title']}</p>";
        }
    }
    
    // Vérifier les routes
    echo "<h2>Test des routes</h2>";
    $routes = [
        'admin/articles',
        'admin/assignEvaluator',
        'admin/publishArticle'
    ];
    
    foreach ($routes as $route) {
        $url = BASE_URL . $route;
        echo "<p>Route '$route': <a href='$url' target='_blank'>$url</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Erreur de connexion à la base de données: " . $e->getMessage() . "</p>";
}

echo "<h2>Configuration</h2>";
echo "<p>BASE_URL: " . BASE_URL . "</p>";
echo "<p>DOI_PREFIX: " . DOI_PREFIX . "</p>";

echo "<h2>Actions recommandées</h2>";
echo "<ol>";
echo "<li>Vérifiez que XAMPP est démarré (Apache et MySQL)</li>";
echo "<li>Importez le fichier donnees.sql dans phpMyAdmin</li>";
echo "<li>Exécutez insert_test_data.php pour créer des données de test</li>";
echo "<li>Connectez-<NAME_EMAIL> / password</li>";
echo "<li>Testez les boutons de publication dans admin/articles</li>";
echo "</ol>";
?>
