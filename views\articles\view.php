<?php include 'views/layout/header.php'; ?>

<div class="row">
    <div class="col-lg-8">
        <article class="article-detail">
            <header class="mb-4">
                <h1><?= htmlspecialchars($article['title']) ?></h1>
                <div class="article-meta">
                    <p class="text-muted">
                        Par <?= htmlspecialchars($article['firstname'] . ' ' . $article['lastname']) ?>
                        - <?= date('d/m/Y', strtotime($article['created_at'])) ?>
                    </p>
                    <span class="badge status-<?= $article['status'] ?>">
                        <?= ucfirst(str_replace('_', ' ', $article['status'])) ?>
                    </span>
                </div>
            </header>
            
            <section class="mb-4">
                <h3>Résumé</h3>
                <p><?= nl2br(htmlspecialchars($article['abstract'])) ?></p>
            </section>
            
            <?php if ($article['keywords']): ?>
                <section class="mb-4">
                    <h3>Mots-clés</h3>
                    <div>
                        <?php foreach (explode(',', $article['keywords']) as $keyword): ?>
                            <span class="badge bg-secondary me-2"><?= trim(htmlspecialchars($keyword)) ?></span>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
            
            <?php if ($article['file_path'] && file_exists($article['file_path'])): ?>
                <section class="mb-4">
                    <h3>Document</h3>
                    <a href="<?= BASE_URL . $article['file_path'] ?>" class="btn btn-primary" target="_blank">
                        <i class="fas fa-file-pdf"></i> Télécharger le PDF
                    </a>
                </section>
            <?php endif; ?>
            
            <?php if ($article['doi']): ?>
                <section class="mb-4">
                    <h3>DOI</h3>
                    <p><code><?= htmlspecialchars($article['doi']) ?></code></p>
                </section>
            <?php endif; ?>
        </article>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5>Informations</h5>
            </div>
            <div class="card-body">
                <p><strong>Statut :</strong> <?= ucfirst(str_replace('_', ' ', $article['status'])) ?></p>
                <p><strong>Soumis le :</strong> <?= date('d/m/Y', strtotime($article['created_at'])) ?></p>
                <?php if ($article['published_at']): ?>
                    <p><strong>Publié le :</strong> <?= date('d/m/Y', strtotime($article['published_at'])) ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="<?= BASE_URL ?>articles" class="btn btn-secondary">Retour à la liste</a>
        </div>
    </div>
</div>

<?php include 'views/layout/footer.php'; ?>