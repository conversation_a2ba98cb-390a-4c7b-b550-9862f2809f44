<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? 'Journal Scientifique Universitaire' ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="<?= BASE_URL ?>assets/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="<?= BASE_URL ?>">Journal Scientifique</a>
            
            <div class="navbar-nav ms-auto">
                <?php
                $currentPage = basename(parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH));
                if (isset($_SESSION['user_id'])) {
                    // Menu pour tous les utilisateurs connectés
                    echo '<a class="nav-link" href="' . BASE_URL . 'dashboard">Tableau de bord</a>';
                    echo '<a class="nav-link" href="' . BASE_URL . 'articles">Articles</a>';
                    if ($_SESSION['role'] === 'evaluateur') {
                        echo '<a class="nav-link" href="' . BASE_URL . 'evaluation">Évaluations</a>';
                    }
                    if ($_SESSION['role'] === 'editeur') {
                        echo '<a class="nav-link" href="' . BASE_URL . 'admin">Administration</a>';
                    }
                    echo '<a class="nav-link" href="' . BASE_URL . 'logout">Déconnexion</a>';
                } else {
                    // Menu pour les visiteurs
                    if ($currentPage !== 'login') {
                        echo '<a class="nav-link" href="' . BASE_URL . 'login">Connexion</a>';
                    }
                    if ($currentPage !== 'register') {
                        echo '<a class="nav-link" href="' . BASE_URL . 'register">Inscription</a>';
                    }
                }
                ?>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">