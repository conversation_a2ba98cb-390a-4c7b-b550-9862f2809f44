<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug des boutons</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Debug des boutons d'administration</h1>
        
        <div class="alert alert-info">
            <h4>Instructions de débogage :</h4>
            <ol>
                <li>Ouvrez la console du navigateur (F12)</li>
                <li>Cliquez sur les boutons ci-dessous</li>
                <li>Vérifiez les messages dans la console</li>
                <li>Vérifiez que les modales s'ouvrent correctement</li>
            </ol>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test du bouton Assigner Évaluateur</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Article :</strong> Analyse des Données et Big Data</p>
                        <p><strong>Statut :</strong> soumis</p>
                        <button class="btn btn-outline-warning assign-btn" 
                                data-id="1"
                                title="Assigner un évaluateur">
                            <i class="fas fa-user-plus"></i> Assigner
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test du bouton Publier</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Article :</strong> Intelligence Artificielle et ML</p>
                        <p><strong>Statut :</strong> accepté</p>
                        <button class="btn btn-outline-success publish-btn" 
                                data-id="2"
                                title="Publier l'article">
                            <i class="fas fa-publish"></i> Publier
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Log des événements :</h3>
            <div id="eventLog" class="border p-3" style="height: 200px; overflow-y: auto; background-color: #f8f9fa;">
                <p><em>Les événements apparaîtront ici...</em></p>
            </div>
        </div>
    </div>

    <!-- Modal pour assigner un évaluateur -->
    <div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignModalLabel">Assigner un Évaluateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignForm">
                        <input type="hidden" id="assign_article_id" name="article_id">
                        <div class="mb-3">
                            <label for="evaluator_id" class="form-label">Sélectionner un évaluateur</label>
                            <select class="form-select" id="evaluator_id" name="evaluator_id" required>
                                <option value="">Choisir un évaluateur...</option>
                                <option value="2">Jean Dupont</option>
                                <option value="3">Marie Martin</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="deadline" class="form-label">Date limite d'évaluation</label>
                            <input type="date" class="form-control" id="deadline" name="deadline" required>
                        </div>
                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instructions pour l'évaluateur (optionnel)</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="3" 
                                      placeholder="Instructions spécifiques pour cette évaluation..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="confirmAssign">Assigner</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour publier un article -->
    <div class="modal fade" id="publishModal" tabindex="-1" aria-labelledby="publishModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="publishModalLabel">Publier un Article</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="publishForm">
                        <input type="hidden" id="publish_article_id" name="article_id">
                        <div class="mb-3">
                            <label for="volume_id" class="form-label">Volume</label>
                            <select class="form-select" id="volume_id" name="volume_id" required>
                                <option value="">Sélectionner un volume...</option>
                                <option value="1">Volume 1 - 2024 (Sciences et Technologies)</option>
                                <option value="2">Volume 2 - 2024 (Recherche Interdisciplinaire)</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="page_start" class="form-label">Page de début</label>
                                    <input type="number" class="form-control" id="page_start" name="page_start" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="page_end" class="form-label">Page de fin</label>
                                    <input type="number" class="form-control" id="page_end" name="page_end" min="1" required>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-success" id="confirmPublish">Publier</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function logEvent(message) {
            const log = document.getElementById('eventLog');
            const time = new Date().toLocaleTimeString();
            log.innerHTML += `<p><strong>[${time}]</strong> ${message}</p>`;
            log.scrollTop = log.scrollHeight;
            console.log(`[${time}] ${message}`);
        }

        document.addEventListener('DOMContentLoaded', function() {
            logEvent('Page chargée, initialisation des événements...');
            
            // Compter les boutons trouvés
            const assignButtons = document.querySelectorAll('.assign-btn');
            const publishButtons = document.querySelectorAll('.publish-btn');
            
            logEvent(`Boutons d'assignation trouvés: ${assignButtons.length}`);
            logEvent(`Boutons de publication trouvés: ${publishButtons.length}`);
            
            // Gestionnaires pour les boutons d'assignation
            assignButtons.forEach((button, index) => {
                logEvent(`Ajout d'événement au bouton d'assignation ${index + 1}`);
                button.addEventListener('click', function() {
                    logEvent('🟡 Bouton assigner cliqué !');
                    const articleId = this.getAttribute('data-id');
                    logEvent(`ID de l'article: ${articleId}`);
                    
                    document.getElementById('assign_article_id').value = articleId;
                    
                    // Définir la date limite par défaut (dans 2 semaines)
                    const deadline = new Date();
                    deadline.setDate(deadline.getDate() + 14);
                    document.getElementById('deadline').value = deadline.toISOString().split('T')[0];
                    
                    logEvent('Ouverture de la modal d\'assignation...');
                    const modal = new bootstrap.Modal(document.getElementById('assignModal'));
                    modal.show();
                });
            });

            // Gestionnaires pour les boutons de publication
            publishButtons.forEach((button, index) => {
                logEvent(`Ajout d'événement au bouton de publication ${index + 1}`);
                button.addEventListener('click', function() {
                    logEvent('🟢 Bouton publier cliqué !');
                    const articleId = this.getAttribute('data-id');
                    logEvent(`ID de l'article: ${articleId}`);
                    
                    document.getElementById('publish_article_id').value = articleId;
                    
                    logEvent('Ouverture de la modal de publication...');
                    const modal = new bootstrap.Modal(document.getElementById('publishModal'));
                    modal.show();
                });
            });

            // Confirmation d'assignation
            document.getElementById('confirmAssign').addEventListener('click', function() {
                logEvent('✅ Confirmation d\'assignation cliquée');
                const form = document.getElementById('assignForm');
                const formData = new FormData(form);
                
                logEvent(`Données: article_id=${formData.get('article_id')}, evaluator_id=${formData.get('evaluator_id')}`);
                alert('Test d\'assignation réussi !');
            });

            // Confirmation de publication
            document.getElementById('confirmPublish').addEventListener('click', function() {
                logEvent('✅ Confirmation de publication cliquée');
                const form = document.getElementById('publishForm');
                const formData = new FormData(form);
                
                logEvent(`Données: article_id=${formData.get('article_id')}, volume_id=${formData.get('volume_id')}`);
                alert('Test de publication réussi !');
            });
            
            logEvent('✅ Initialisation terminée');
        });
    </script>
</body>
</html>
