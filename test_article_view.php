<?php
require_once 'init.php';

// Test de la méthode findByIdWithDetails
$articleModel = new Article();

echo "<h1>Test de la vue d'article</h1>";

// Récupérer tous les articles pour voir les IDs disponibles
$allArticles = $articleModel->findAll();
echo "<h2>Articles disponibles :</h2>";
echo "<ul>";
foreach ($allArticles as $article) {
    echo "<li>ID: {$article['id']} - Titre: " . htmlspecialchars($article['title']) . "</li>";
}
echo "</ul>";

// Tester avec le premier article
if (!empty($allArticles)) {
    $firstArticleId = $allArticles[0]['id'];
    echo "<h2>Test avec l'article ID: $firstArticleId</h2>";
    
    // Test de l'ancienne méthode (sans détails)
    echo "<h3>Ancienne méthode (findById) :</h3>";
    $articleOld = $articleModel->findById($firstArticleId);
    echo "<pre>";
    print_r($articleOld);
    echo "</pre>";
    
    // Test de la nouvelle méthode (avec détails)
    echo "<h3>Nouvelle méthode (findByIdWithDetails) :</h3>";
    $articleNew = $articleModel->findByIdWithDetails($firstArticleId);
    echo "<pre>";
    print_r($articleNew);
    echo "</pre>";
    
    // Vérifier si les champs firstname et lastname sont présents
    if (isset($articleNew['firstname']) && isset($articleNew['lastname'])) {
        echo "<div style='color: green; font-weight: bold;'>✅ SUCCESS: Les champs firstname et lastname sont présents !</div>";
        echo "<p>Auteur: " . htmlspecialchars($articleNew['firstname'] . ' ' . $articleNew['lastname']) . "</p>";
    } else {
        echo "<div style='color: red; font-weight: bold;'>❌ ERROR: Les champs firstname et lastname sont manquants !</div>";
    }
    
    // Lien pour tester la vraie vue
    echo "<h3>Test de la vraie vue :</h3>";
    echo "<a href='" . BASE_URL . "articles/view?id=$firstArticleId' target='_blank' class='btn btn-primary'>Voir l'article dans la vraie vue</a>";
    
} else {
    echo "<p style='color: red;'>Aucun article trouvé dans la base de données.</p>";
    echo "<p>Exécutez d'abord le script de création de données de test.</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
ul { background: #f9f9f9; padding: 15px; border-radius: 5px; }
.btn { 
    display: inline-block; 
    padding: 10px 20px; 
    background: #007bff; 
    color: white; 
    text-decoration: none; 
    border-radius: 5px; 
    margin: 10px 0;
}
.btn:hover { background: #0056b3; }
</style>
