<?php include 'views/layout/header.php'; ?>

<div class="row">
    <div class="col-lg-8">
        <article class="article-detail">
            <header class="mb-4">
                <h1><?= htmlspecialchars($article['title']) ?></h1>
                <div class="article-meta">
                    <p class="text-muted">
                        Par <?= htmlspecialchars($article['firstname'] . ' ' . $article['lastname']) ?>
                        - <?= date('d/m/Y', strtotime($article['created_at'])) ?>
                    </p>
                    <span class="badge status-<?= $article['status'] ?>">
                        <?= ucfirst(str_replace('_', ' ', $article['status'])) ?>
                    </span>
                </div>
            </header>
            
            <section class="mb-4">
                <h3>Résumé</h3>
                <p><?= nl2br(htmlspecialchars($article['abstract'])) ?></p>
            </section>
            
            <?php if ($article['keywords']): ?>
                <section class="mb-4">
                    <h3>Mots-clés</h3>
                    <div>
                        <?php foreach (explode(',', $article['keywords']) as $keyword): ?>
                            <span class="badge bg-secondary me-2"><?= trim(htmlspecialchars($keyword)) ?></span>
                        <?php endforeach; ?>
                    </div>
                </section>
            <?php endif; ?>
            
            <?php if ($article['file_path']): ?>
                <section class="mb-4">
                    <h3>Document</h3>
                    <?php if (file_exists($article['file_path'])): ?>
                        <a href="<?= BASE_URL . str_replace(BASE_PATH, '', $article['file_path']) ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-file-pdf"></i> Télécharger le PDF
                        </a>
                    <?php else: ?>
                        <p class="text-muted">
                            <i class="fas fa-exclamation-triangle"></i> Fichier non trouvé
                        </p>
                    <?php endif; ?>
                </section>
            <?php endif; ?>
            
            <?php if ($article['doi']): ?>
                <section class="mb-4">
                    <h3>DOI</h3>
                    <p><code><?= htmlspecialchars($article['doi']) ?></code></p>
                </section>
            <?php endif; ?>
        </article>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5>Informations</h5>
            </div>
            <div class="card-body">
                <p><strong>Auteur :</strong> <?= htmlspecialchars($article['firstname'] . ' ' . $article['lastname']) ?></p>
                <?php if (!empty($article['email'])): ?>
                    <p><strong>Email :</strong> <?= htmlspecialchars($article['email']) ?></p>
                <?php endif; ?>
                <?php if (!empty($article['institution'])): ?>
                    <p><strong>Institution :</strong> <?= htmlspecialchars($article['institution']) ?></p>
                <?php endif; ?>
                <?php if (!empty($article['speciality'])): ?>
                    <p><strong>Spécialité :</strong> <?= htmlspecialchars($article['speciality']) ?></p>
                <?php endif; ?>
                <hr>
                <p><strong>Statut :</strong> <?= ucfirst(str_replace('_', ' ', $article['status'])) ?></p>
                <p><strong>Soumis le :</strong> <?= date('d/m/Y', strtotime($article['created_at'])) ?></p>
                <?php if ($article['published_at']): ?>
                    <p><strong>Publié le :</strong> <?= date('d/m/Y', strtotime($article['published_at'])) ?></p>
                <?php endif; ?>
                <?php if (!empty($article['domain_name'])): ?>
                    <p><strong>Domaine :</strong> <?= htmlspecialchars($article['domain_name']) ?></p>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="<?= BASE_URL ?>articles" class="btn btn-secondary">Retour à la liste</a>
        </div>
    </div>
</div>

<?php include 'views/layout/footer.php'; ?>