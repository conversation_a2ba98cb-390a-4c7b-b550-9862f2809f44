// File: /web-project/web-project/public/js/animations.js
document.addEventListener('DOMContentLoaded', function() {
    const elements = document.querySelectorAll('.animate');

    elements.forEach(element => {
        element.style.opacity = 0;
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            element.style.opacity = 1;
            element.style.transform = 'translateY(0)';
        }, 100);
    });

    window.addEventListener('scroll', () => {
        elements.forEach(element => {
            const rect = element.getBoundingClientRect();
            if (rect.top < window.innerHeight && rect.bottom > 0) {
                element.style.opacity = 1;
                element.style.transform = 'translateY(0)';
            } else {
                element.style.opacity = 0;
                element.style.transform = 'translateY(20px)';
            }
        });
    });
});