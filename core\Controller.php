<?php
class Controller {
    protected function loadModel($modelName) {
        require_once MODELS_PATH . "/{$modelName}.php";
        return new $modelName();
    }
    
    protected function loadView($viewName, $data = []) {
        extract($data);
        require_once VIEWS_PATH . "/{$viewName}.php";
    }
    
    protected function redirect($url) {
        header("Location: " . BASE_URL . $url);
        exit();
    }
    
    protected function isLoggedIn() {
        return isset($_SESSION['user_id']);
    }
    
    protected function requireLogin() {
        if (!$this->isLoggedIn()) {
            $this->redirect('login');
        }
    }
    
    protected function hasRole($role) {
        return isset($_SESSION['role']) && $_SESSION['role'] === $role;
    }
}
?>