<?php
class AdminController extends Controller {
    
    public function index() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $articleModel = $this->loadModel('Article');
        $userModel = $this->loadModel('User');
        $volumeModel = $this->loadModel('Volume');
        
        $stats = [
            'totalArticles' => $articleModel->getTotalCount(),
            'submittedArticles' => count($articleModel->getByStatus('soumis')),
            'publishedArticles' => count($articleModel->getByStatus('publie')),
            'totalUsers' => $userModel->getTotalCount(),
            'totalVolumes' => $volumeModel->getTotalCount()
        ];
        
        $this->loadView('admin/index', ['stats' => $stats]);
    }
    
    public function articles() {
        $this->requireLogin();

        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }

        $articleModel = $this->loadModel('Article');
        $userModel = $this->loadModel('User');
        $volumeModel = $this->loadModel('Volume');

        $articles = $articleModel->getAllWithDetails();
        $evaluators = $userModel->getByRole('evaluateur');
        $volumes = $volumeModel->findAll();

        $this->loadView('admin/articles', [
            'articles' => $articles,
            'evaluators' => $evaluators,
            'volumes' => $volumes
        ]);
    }
    
    public function assignEvaluator() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $articleId = $_POST['article_id'] ?? 0;
        $evaluatorId = $_POST['evaluator_id'] ?? 0;
        
        $evaluationModel = $this->loadModel('Evaluation');
        $evaluationData = [
            'article_id' => $articleId,
            'evaluator_id' => $evaluatorId,
            'status' => 'en_cours',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        if ($evaluationModel->create($evaluationData)) {
            // Mettre à jour le statut de l'article
            $articleModel = $this->loadModel('Article');
            $articleModel->updateStatus($articleId, 'en_evaluation');
            
            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false]);
        }
    }
    
    public function publishArticle() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $articleId = $_POST['article_id'] ?? 0;
        $volumeId = $_POST['volume_id'] ?? 0;
        $pageStart = $_POST['page_start'] ?? 0;
        $pageEnd = $_POST['page_end'] ?? 0;
        
        $articleModel = $this->loadModel('Article');
        $doi = $this->generateDOI($articleId);
        
        $updateData = [
            'status' => 'publie',
            'doi' => $doi,
            'volume_id' => $volumeId,
            'page_start' => $pageStart,
            'page_end' => $pageEnd,
            'published_at' => date('Y-m-d H:i:s')
        ];
        
        if ($articleModel->update($articleId, $updateData)) {
            echo json_encode(['success' => true, 'doi' => $doi]);
        } else {
            echo json_encode(['success' => false]);
        }
    }
    
    private function generateDOI($articleId) {
        return DOI_PREFIX . '.' . date('Y') . '.' . str_pad($articleId, 6, '0', STR_PAD_LEFT);
    }
    
    public function volumes() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        $volumeModel = $this->loadModel('Volume');
        $volumes = $volumeModel->getAllWithArticleCount();
        
        $this->loadView('admin/volumes', ['volumes' => $volumes]);
    }
    
    public function createVolume() {
        $this->requireLogin();
        
        if (!$this->hasRole('editeur')) {
            $this->redirect('dashboard');
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $volumeModel = $this->loadModel('Volume');
            $volumeData = [
                'number' => $_POST['number'],
                'year' => $_POST['year'],
                'title' => $_POST['title'],
                'description' => $_POST['description'],
                'publication_date' => $_POST['publication_date'],
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            if ($volumeModel->create($volumeData)) {
                $this->redirect('admin/volumes?success=1');
            }
        }
        
        $this->loadView('admin/create_volume');
    }
}
?>