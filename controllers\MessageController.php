<?php
class Message<PERSON>ontroller extends Controller {
    
    public function index() {
        $this->requireLogin();
        
        $messageModel = $this->loadModel('Message');
        $userId = $_SESSION['user_id'];
        
        $messages = $messageModel->getByUser($userId);
        
        $this->loadView('messages/index', ['messages' => $messages]);
    }
    
    public function send() {
        $this->requireLogin();
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $messageModel = $this->loadModel('Message');
            
            $messageData = [
                'article_id' => $_POST['article_id'],
                'sender_id' => $_SESSION['user_id'],
                'recipient_id' => $_POST['recipient_id'],
                'subject' => $_POST['subject'],
                'content' => $_POST['content'],
                'is_anonymous' => isset($_POST['is_anonymous']) ? 1 : 0,
                'created_at' => date('Y-m-d H:i:s')
            ];
            
            if ($messageModel->create($messageData)) {
                echo json_encode(['success' => true]);
            } else {
                echo json_encode(['success' => false]);
            }
        }
    }
    
    public function markAsRead() {
        $this->requireLogin();
        
        $messageId = $_POST['message_id'] ?? 0;
        $messageModel = $this->loadModel('Message');
        
        $messageModel->markAsRead($messageId, $_SESSION['user_id']);
        echo json_encode(['success' => true]);
    }
}
?>