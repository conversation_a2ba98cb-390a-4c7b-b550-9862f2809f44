<?php include 'views/layout/header.php'; ?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h4>Inscription</h4>
                <a href="<?= BASE_URL ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour à l'accueil
                </a>
            </div>
            <div class="card-body">
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                                <li><?= $error ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
                
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="firstname" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="firstname" name="firstname" 
                                       value="<?= $_POST['firstname'] ?? '' ?>" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="lastname" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="lastname" name="lastname" 
                                       value="<?= $_POST['lastname'] ?? '' ?>" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= $_POST['email'] ?? '' ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="role" class="form-label">Rôle *</label>
                        <select class="form-control" id="role" name="role" required>
                            <option value="auteur" <?= ($_POST['role'] ?? '') === 'auteur' ? 'selected' : '' ?>>Auteur</option>
                            <option value="evaluateur" <?= ($_POST['role'] ?? '') === 'evaluateur' ? 'selected' : '' ?>>Évaluateur</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="institution" class="form-label">Institution</label>
                        <input type="text" class="form-control" id="institution" name="institution" 
                               value="<?= $_POST['institution'] ?? '' ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="speciality" class="form-label">Spécialité</label>
                        <input type="text" class="form-control" id="speciality" name="speciality" 
                               value="<?= $_POST['speciality'] ?? '' ?>">
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">S'inscrire</button>
                        <a href="<?= BASE_URL ?>login" class="btn btn-link">Déjà inscrit ?</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php include 'views/layout/footer.php'; ?>
