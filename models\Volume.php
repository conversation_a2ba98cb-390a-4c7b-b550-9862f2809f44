<?php
class Volume extends Model {
    protected $table = 'volumes';
    
    public function getAllWithArticleCount() {
        $stmt = $this->db->prepare("
            SELECT v.*, COUNT(a.id) as article_count 
            FROM volumes v 
            LEFT JOIN articles a ON v.id = a.volume_id 
            GROUP BY v.id 
            ORDER BY v.year DESC, v.number DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getTotalCount() {
        $stmt = $this->db->prepare("SELECT COUNT(*) as count FROM volumes");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    }
    
    public function publish($id) {
        $stmt = $this->db->prepare("UPDATE volumes SET is_published = 1 WHERE id = ?");
        return $stmt->execute([$id]);
    }
}
?>