<?php include BASE_PATH . '/views/layout/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-book"></i> Gestion des Volumes</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createVolumeModal">
                    <i class="fas fa-plus"></i> Nouveau Volume
                </button>
            </div>

            <?php if (isset($_GET['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle"></i> Volume créé avec succès !
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-triangle"></i> Erreur lors de la création du volume.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="row">
        <?php foreach ($volumes as $volume): ?>
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card volume-card h-100 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-book-open"></i> 
                        Volume <?= $volume['number'] ?> - <?= $volume['year'] ?>
                    </h5>
                </div>
                <div class="card-body">
                    <h6 class="card-title"><?= htmlspecialchars($volume['title']) ?></h6>
                    <p class="card-text text-muted"><?= htmlspecialchars($volume['description']) ?></p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="badge bg-info"><?= $volume['article_count'] ?> articles</span>
                        <span class="text-muted"><?= date('d/m/Y', strtotime($volume['created_at'])) ?></span>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit"></i> Modifier
                        </button>
                        <button class="btn btn-outline-danger btn-sm">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>

<!-- Modal pour créer un volume -->
<div class="modal fade" id="createVolumeModal" tabindex="-1" aria-labelledby="createVolumeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createVolumeModalLabel">
                    <i class="fas fa-plus-circle"></i> Créer un Nouveau Volume
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createVolumeForm" method="POST" action="<?= BASE_URL ?>admin/createVolume">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="volume_number" class="form-label">Numéro du Volume *</label>
                                <input type="number" class="form-control" id="volume_number" name="number" required min="1">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="volume_year" class="form-label">Année *</label>
                                <input type="number" class="form-control" id="volume_year" name="year"
                                       required min="2020" max="2030" value="<?= date('Y') ?>">
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="volume_title" class="form-label">Titre du Volume *</label>
                        <input type="text" class="form-control" id="volume_title" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="volume_description" class="form-label">Description</label>
                        <textarea class="form-control" id="volume_description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="publication_date" class="form-label">Date de Publication</label>
                        <input type="date" class="form-control" id="publication_date" name="publication_date">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> Annuler
                </button>
                <button type="button" class="btn btn-primary" id="confirmCreateVolume">
                    <i class="fas fa-save"></i> Créer le Volume
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Animation des cartes avec bulles flottantes
document.addEventListener('DOMContentLoaded', function() {
    // Créer des bulles animées en arrière-plan
    createFloatingBubbles();

    // Animation des cartes
    const cards = document.querySelectorAll('.volume-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px) scale(0.9)';
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0) scale(1)';
        }, index * 100);
    });

    // Gestionnaire pour créer un volume
    document.getElementById('confirmCreateVolume').addEventListener('click', function() {
        const form = document.getElementById('createVolumeForm');
        const formData = new FormData(form);

        // Validation
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        // Envoyer la requête
        fetch('<?= BASE_URL ?>admin/createVolume', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (response.ok) {
                // Redirection avec message de succès
                window.location.href = '<?= BASE_URL ?>admin/volumes?success=1';
            } else {
                throw new Error('Erreur lors de la création');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la création du volume');
        });
    });
});

// Fonction pour créer des bulles flottantes
function createFloatingBubbles() {
    const container = document.body;
    const colors = ['#007bff', '#28a745', '#17a2b8', '#ffc107', '#dc3545', '#6f42c1'];

    for (let i = 0; i < 15; i++) {
        const bubble = document.createElement('div');
        bubble.className = 'floating-bubble';
        bubble.style.cssText = `
            position: fixed;
            width: ${Math.random() * 60 + 20}px;
            height: ${Math.random() * 60 + 20}px;
            background: ${colors[Math.floor(Math.random() * colors.length)]}20;
            border-radius: 50%;
            pointer-events: none;
            z-index: -1;
            left: ${Math.random() * 100}vw;
            top: ${Math.random() * 100}vh;
            animation: float ${Math.random() * 10 + 10}s infinite linear;
        `;
        container.appendChild(bubble);
    }
}
</script>

<style>
@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

.floating-bubble {
    animation: float linear infinite;
}

.volume-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.volume-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1) !important;
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3) !important;
    border-radius: 15px 15px 0 0 !important;
}

.btn-group .btn {
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-2px);
}

.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
}

.modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}
</style>

<?php include BASE_PATH . '/views/layout/footer.php'; ?>