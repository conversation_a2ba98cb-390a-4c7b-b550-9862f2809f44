<?php include 'views/layout/header.php'; ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Mes Articles</h1>
    <a href="<?= BASE_URL ?>articles/submit" class="btn btn-success">Soumettre un article</a>
</div>

<?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success">Article soumis avec succès !</div>
<?php endif; ?>

<?php if (!empty($articles)): ?>
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>Titre</th>
                    <th>Domaine</th>
                    <th>Statut</th>
                    <th>Date de soumission</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($articles as $article): ?>
                    <tr>
                        <td><?= htmlspecialchars($article['title']) ?></td>
                        <td><?= htmlspecialchars($article['domain_name'] ?? 'Non spécifié') ?></td>
                        <td>
                            <span class="badge status-<?= $article['status'] ?>">
                                <?= ucfirst(str_replace('_', ' ', $article['status'])) ?>
                            </span>
                        </td>
                        <td><?= date('d/m/Y', strtotime($article['created_at'])) ?></td>
                        <td>
                            <a href="<?= BASE_URL ?>articles/view?id=<?= $article['id'] ?>" class="btn btn-sm btn-primary">Voir</a>
                            <?php if ($article['status'] === 'soumis'): ?>
                                <button class="btn btn-sm btn-warning">Modifier</button>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <div class="text-center py-5">
        <h3>Aucun article</h3>
        <p>Vous n'avez pas encore soumis d'articles.</p>
        <a href="<?= BASE_URL ?>articles/submit" class="btn btn-success">Soumettre votre premier article</a>
    </div>
<?php endif; ?>

<?php include 'views/layout/footer.php'; ?>