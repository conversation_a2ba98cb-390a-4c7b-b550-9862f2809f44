<?php
class Router {
    private $routes = [];
    
    public function __construct() {
        $this->defineRoutes();
    }
    
    private function defineRoutes() {
        $this->routes = [
            '' => ['controller' => 'HomeController', 'action' => 'index'],
            'home' => ['controller' => 'HomeController', 'action' => 'index'],
            'login' => ['controller' => 'AuthController', 'action' => 'login'],
            'register' => ['controller' => 'AuthController', 'action' => 'register'],
            'logout' => ['controller' => 'AuthController', 'action' => 'logout'],
            'dashboard' => ['controller' => 'DashboardController', 'action' => 'index'],
            'articles' => ['controller' => 'ArticleController', 'action' => 'index'],
            'articles/submit' => ['controller' => 'ArticleController', 'action' => 'submit'],
            'articles/view' => ['controller' => 'ArticleController', 'action' => 'view'],
            'evaluation' => ['controller' => 'EvaluationController', 'action' => 'index'],
            'admin' => ['controller' => 'AdminController', 'action' => 'index'],
            'admin/articles' => ['controller' => 'AdminController', 'action' => 'articles'],
            'admin/users' => ['controller' => 'AdminController', 'action' => 'users'],
            'admin/volumes' => ['controller' => 'AdminController', 'action' => 'volumes'],
        ];
    }
    
    public function handleRequest() {
        $uri = $this->parseUri();
        
        // Debug pour voir l'URI parsée
        if (isset($_GET['debug'])) {
            echo "URI parsée: '$uri'<br>";
            echo "Routes disponibles: " . implode(', ', array_keys($this->routes)) . "<br>";
        }
        
        if (isset($this->routes[$uri])) {
            $route = $this->routes[$uri];
            $this->loadController($route['controller'], $route['action']);
        } else {
            // Log de l'erreur 404 pour debug
            error_log("404 Error - URI not found: '$uri'");
            $this->loadController('ErrorController', 'notFound');
        }
    }
    
    private function parseUri() {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        
        // Supprimer les paramètres GET
        $uri = parse_url($uri, PHP_URL_PATH);
        
        // Supprimer les slashes de début et fin
        $uri = trim($uri, '/');
        
        // Supprimer le nom du dossier du projet si présent
        $basePath = trim(parse_url(BASE_URL, PHP_URL_PATH), '/');
        if ($basePath && strpos($uri, $basePath) === 0) {
            $uri = substr($uri, strlen($basePath));
            $uri = trim($uri, '/');
        }
        
        return $uri;
    }
    
    private function loadController($controllerName, $action) {
        $controllerPath = CONTROLLERS_PATH . "/{$controllerName}.php";
        
        if (!file_exists($controllerPath)) {
            error_log("Controller not found: $controllerPath");
            $this->loadController('ErrorController', 'notFound');
            return;
        }
        
        require_once $controllerPath;
        
        if (!class_exists($controllerName)) {
            error_log("Controller class not found: $controllerName");
            $this->loadController('ErrorController', 'notFound');
            return;
        }
        
        $controller = new $controllerName();
        
        if (!method_exists($controller, $action)) {
            error_log("Controller method not found: $controllerName::$action");
            $this->loadController('ErrorController', 'notFound');
            return;
        }
        
        $controller->$action();
    }
}
?>
