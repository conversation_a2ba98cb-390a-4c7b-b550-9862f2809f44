<?php
require_once 'init.php';

// Charger les données comme le fait le contrôleur
$userModel = new User();
$volumeModel = new Volume();

$evaluators = $userModel->getByRole('evaluateur');
$volumes = $volumeModel->findAll();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des formulaires</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test des formulaires</h1>
        
        <div class="alert alert-info">
            <h4>Données disponibles :</h4>
            <p><strong>Évaluateurs :</strong> <?= count($evaluators) ?> trouvé(s)</p>
            <p><strong>Volumes :</strong> <?= count($volumes) ?> trouvé(s)</p>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test formulaire d'assignation</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#assignModal">
                            Ouvrir formulaire d'assignation
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test formulaire de publication</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#publishModal">
                            Ouvrir formulaire de publication
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Test direct des champs :</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>Sélection d'évaluateur :</h5>
                    <select class="form-select" id="test-evaluator">
                        <option value="">Choisir un évaluateur...</option>
                        <?php foreach ($evaluators as $evaluator): ?>
                            <option value="<?= $evaluator['id'] ?>">
                                <?= htmlspecialchars($evaluator['firstname'] . ' ' . $evaluator['lastname']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-6">
                    <h5>Sélection de volume :</h5>
                    <select class="form-select" id="test-volume">
                        <option value="">Sélectionner un volume...</option>
                        <?php foreach ($volumes as $volume): ?>
                            <option value="<?= $volume['id'] ?>">
                                Volume <?= $volume['number'] ?> - <?= $volume['year'] ?> (<?= htmlspecialchars($volume['title']) ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-md-6">
                    <h5>Date limite :</h5>
                    <input type="date" class="form-control" id="test-date">
                </div>
                
                <div class="col-md-6">
                    <h5>Pages :</h5>
                    <div class="row">
                        <div class="col-6">
                            <input type="number" class="form-control" placeholder="Page début" id="test-page-start">
                        </div>
                        <div class="col-6">
                            <input type="number" class="form-control" placeholder="Page fin" id="test-page-end">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <h5>Instructions :</h5>
                <textarea class="form-control" rows="3" placeholder="Tapez vos instructions ici..." id="test-instructions"></textarea>
            </div>
        </div>
    </div>

    <!-- Modal pour assigner un évaluateur -->
    <div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignModalLabel">Assigner un Évaluateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignForm">
                        <input type="hidden" id="assign_article_id" name="article_id" value="123">
                        <div class="mb-3">
                            <label for="evaluator_id" class="form-label">Sélectionner un évaluateur</label>
                            <select class="form-select" id="evaluator_id" name="evaluator_id" required>
                                <option value="">Choisir un évaluateur...</option>
                                <?php foreach ($evaluators as $evaluator): ?>
                                    <option value="<?= $evaluator['id'] ?>">
                                        <?= htmlspecialchars($evaluator['firstname'] . ' ' . $evaluator['lastname']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="deadline" class="form-label">Date limite d'évaluation</label>
                            <input type="date" class="form-control" id="deadline" name="deadline" required>
                        </div>
                        <div class="mb-3">
                            <label for="instructions" class="form-label">Instructions pour l'évaluateur (optionnel)</label>
                            <textarea class="form-control" id="instructions" name="instructions" rows="3" 
                                      placeholder="Instructions spécifiques pour cette évaluation..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="confirmAssign">Assigner</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour publier un article -->
    <div class="modal fade" id="publishModal" tabindex="-1" aria-labelledby="publishModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="publishModalLabel">Publier un Article</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="publishForm">
                        <input type="hidden" id="publish_article_id" name="article_id" value="456">
                        <div class="mb-3">
                            <label for="volume_id" class="form-label">Volume</label>
                            <select class="form-select" id="volume_id" name="volume_id" required>
                                <option value="">Sélectionner un volume...</option>
                                <?php foreach ($volumes as $volume): ?>
                                    <option value="<?= $volume['id'] ?>">
                                        Volume <?= $volume['number'] ?> - <?= $volume['year'] ?> (<?= htmlspecialchars($volume['title']) ?>)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="page_start" class="form-label">Page de début</label>
                                    <input type="number" class="form-control" id="page_start" name="page_start" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="page_end" class="form-label">Page de fin</label>
                                    <input type="number" class="form-control" id="page_end" name="page_end" min="1" required>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-success" id="confirmPublish">Publier</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test des formulaires initialisé');
            
            // Définir une date par défaut
            const deadline = new Date();
            deadline.setDate(deadline.getDate() + 14);
            const dateString = deadline.toISOString().split('T')[0];
            
            document.getElementById('deadline').value = dateString;
            document.getElementById('test-date').value = dateString;
            
            // Test des événements de changement
            document.getElementById('test-evaluator').addEventListener('change', function() {
                console.log('Évaluateur sélectionné:', this.value, this.options[this.selectedIndex].text);
            });
            
            document.getElementById('test-volume').addEventListener('change', function() {
                console.log('Volume sélectionné:', this.value, this.options[this.selectedIndex].text);
            });
            
            document.getElementById('test-date').addEventListener('change', function() {
                console.log('Date sélectionnée:', this.value);
            });
            
            document.getElementById('test-instructions').addEventListener('input', function() {
                console.log('Instructions tapées:', this.value);
            });
            
            // Test des boutons de confirmation
            document.getElementById('confirmAssign').addEventListener('click', function() {
                const form = document.getElementById('assignForm');
                const formData = new FormData(form);
                
                console.log('Données d\'assignation:', {
                    article_id: formData.get('article_id'),
                    evaluator_id: formData.get('evaluator_id'),
                    deadline: formData.get('deadline'),
                    instructions: formData.get('instructions')
                });
                
                alert('Formulaire d\'assignation testé avec succès !');
            });
            
            document.getElementById('confirmPublish').addEventListener('click', function() {
                const form = document.getElementById('publishForm');
                const formData = new FormData(form);
                
                console.log('Données de publication:', {
                    article_id: formData.get('article_id'),
                    volume_id: formData.get('volume_id'),
                    page_start: formData.get('page_start'),
                    page_end: formData.get('page_end')
                });
                
                alert('Formulaire de publication testé avec succès !');
            });
        });
    </script>
</body>
</html>
