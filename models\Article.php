<?php
class Article extends Model {
    protected $table = 'articles';
    
    public function getByAuthor($authorId) {
        try {
            $stmt = $this->db->prepare("
                SELECT a.*, u.firstname, u.lastname, d.name as domain_name
                FROM articles a 
                JOIN users u ON a.author_id = u.id 
                LEFT JOIN domains d ON a.domain_id = d.id
                WHERE a.author_id = ?
                ORDER BY a.created_at DESC
            ");
            $stmt->execute([$authorId]);
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            return $result ?: []; // Retourner un tableau vide si null
        } catch (PDOException $e) {
            error_log("Erreur getByAuthor: " . $e->getMessage());
            return []; // Retourner un tableau vide en cas d'erreur
        }
    }
    
    public function getByStatus($status) {
        try {
            $stmt = $this->db->prepare("
                SELECT a.*, u.firstname, u.lastname 
                FROM articles a 
                JOIN users u ON a.author_id = u.id 
                WHERE a.status = ?
                ORDER BY a.created_at DESC
            ");
            $stmt->execute([$status]);
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            return $result ?: []; // Retourner un tableau vide si null
        } catch (PDOException $e) {
            error_log("Erreur getByStatus: " . $e->getMessage());
            return []; // Retourner un tableau vide en cas d'erreur
        }
    }
    
    public function getTotalCount() {
        try {
            $stmt = $this->db->query("SELECT COUNT(*) as total FROM articles");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (int)$result['total'] : 0;
        } catch (PDOException $e) {
            error_log("Erreur getTotalCount: " . $e->getMessage());
            return 0; // Retourner 0 en cas d'erreur
        }
    }
    
    public function updateStatus($id, $status) {
        $stmt = $this->db->prepare("UPDATE articles SET status = ?, updated_at = NOW() WHERE id = ?");
        return $stmt->execute([$status, $id]);
    }
    
    public function assignDOI($id, $doi) {
        $stmt = $this->db->prepare("UPDATE articles SET doi = ? WHERE id = ?");
        return $stmt->execute([$doi, $id]);
    }
    
    public function search($keywords, $domain = null) {
        $sql = "
            SELECT a.*, u.firstname, u.lastname 
            FROM articles a 
            JOIN users u ON a.author_id = u.id 
            WHERE (a.title LIKE ? OR a.abstract LIKE ? OR a.keywords LIKE ?)
        ";
        $params = ["%{$keywords}%", "%{$keywords}%", "%{$keywords}%"];
        
        if ($domain) {
            $sql .= " AND a.domain = ?";
            $params[] = $domain;
        }
        
        $sql .= " ORDER BY a.created_at DESC";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getAllWithDetails() {
        $stmt = $this->db->prepare("
            SELECT a.*, u.firstname, u.lastname, d.name as domain_name
            FROM articles a
            JOIN users u ON a.author_id = u.id
            LEFT JOIN domains d ON a.domain_id = d.id
            ORDER BY a.created_at DESC
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function findByIdWithDetails($id) {
        $stmt = $this->db->prepare("
            SELECT a.*, u.firstname, u.lastname, u.email, u.institution, u.speciality, d.name as domain_name
            FROM articles a
            JOIN users u ON a.author_id = u.id
            LEFT JOIN domains d ON a.domain_id = d.id
            WHERE a.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    public function getPublishedArticles($limit = null) {
        $sql = "
            SELECT a.*, u.firstname, u.lastname, d.name as domain_name
            FROM articles a 
            JOIN users u ON a.author_id = u.id
            LEFT JOIN domains d ON a.domain_id = d.id
            WHERE a.status = 'publie'
            ORDER BY a.published_at DESC
        ";
        
        if ($limit) {
            $sql .= " LIMIT " . (int)$limit;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function update($id, $data) {
        $updates = [];
        foreach ($data as $key => $value) {
            $updates[] = "$key = :$key";
        }
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $updates) . " WHERE id = :id";
        $data['id'] = $id;
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($data);
    }
}
?>

