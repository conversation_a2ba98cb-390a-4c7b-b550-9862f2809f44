<?php
class HomeController extends Controller {
    
    public function index() {
        $articleModel = $this->loadModel('Article');
        $domainModel = $this->loadModel('Domain');
        
        // Articles récents publiés
        $recentArticles = $articleModel->getPublishedArticles(5);
        $domains = $domainModel->findAll();
        
        $this->loadView('home/index', [
            'recentArticles' => $recentArticles,
            'domains' => $domains
        ]);
    }
    
    public function search() {
        $query = $_GET['q'] ?? '';
        $domain = $_GET['domain'] ?? null;
        
        $articleModel = $this->loadModel('Article');
        $results = [];
        
        if (!empty($query)) {
            $results = $articleModel->search($query, $domain);
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['ajax'])) {
            header('Content-Type: application/json');
            echo json_encode($results);
            exit;
        }
        
        $this->loadView('home/search', [
            'results' => $results,
            'query' => $query,
            'domain' => $domain
        ]);
    }
}
?>