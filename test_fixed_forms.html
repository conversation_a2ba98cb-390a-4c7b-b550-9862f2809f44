<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des formulaires corrigés</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test des formulaires avec IDs corrigés</h1>
        
        <div class="alert alert-success">
            <h4>✅ Corrections apportées :</h4>
            <ul>
                <li>IDs uniques pour chaque modal (assign_ et publish_ préfixes)</li>
                <li>Pas de conflits entre les formulaires</li>
                <li>JavaScript mis à jour pour utiliser les nouveaux IDs</li>
            </ul>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Assignation d'Évaluateur</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Article :</strong> Analyse des Données et Big Data</p>
                        <p><strong>Statut :</strong> soumis</p>
                        <button class="btn btn-outline-warning assign-btn" 
                                data-id="1"
                                title="Assigner un évaluateur">
                            <i class="fas fa-user-plus"></i> Assigner
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Test Publication d'Article</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Article :</strong> Intelligence Artificielle et ML</p>
                        <p><strong>Statut :</strong> accepté</p>
                        <button class="btn btn-outline-success publish-btn" 
                                data-id="2"
                                title="Publier l'article">
                            <i class="fas fa-publish"></i> Publier
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Instructions de test :</h3>
            <ol>
                <li>Cliquez sur le bouton "Assigner" - la modal devrait s'ouvrir</li>
                <li>Essayez de sélectionner un évaluateur dans la liste déroulante</li>
                <li>Essayez de modifier la date limite</li>
                <li>Tapez des instructions dans la zone de texte</li>
                <li>Fermez la modal et testez le bouton "Publier"</li>
                <li>Vérifiez que vous pouvez remplir tous les champs</li>
            </ol>
        </div>
    </div>

    <!-- Modal pour assigner un évaluateur -->
    <div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="assignModalLabel">Assigner un Évaluateur</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="assignForm">
                        <input type="hidden" id="assign_article_id" name="article_id">
                        <div class="mb-3">
                            <label for="assign_evaluator_id" class="form-label">Sélectionner un évaluateur</label>
                            <select class="form-select" id="assign_evaluator_id" name="evaluator_id" required>
                                <option value="">Choisir un évaluateur...</option>
                                <option value="2">Jean Dupont - Université de Paris</option>
                                <option value="3">Marie Martin - Université de Lyon</option>
                                <option value="4">Pierre Durand - Université de Marseille</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="assign_deadline" class="form-label">Date limite d'évaluation</label>
                            <input type="date" class="form-control" id="assign_deadline" name="deadline" required>
                        </div>
                        <div class="mb-3">
                            <label for="assign_instructions" class="form-label">Instructions pour l'évaluateur (optionnel)</label>
                            <textarea class="form-control" id="assign_instructions" name="instructions" rows="3" 
                                      placeholder="Instructions spécifiques pour cette évaluation..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" id="confirmAssign">Assigner</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal pour publier un article -->
    <div class="modal fade" id="publishModal" tabindex="-1" aria-labelledby="publishModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="publishModalLabel">Publier un Article</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="publishForm">
                        <input type="hidden" id="publish_article_id" name="article_id">
                        <div class="mb-3">
                            <label for="publish_volume_id" class="form-label">Volume</label>
                            <select class="form-select" id="publish_volume_id" name="volume_id" required>
                                <option value="">Sélectionner un volume...</option>
                                <option value="1">Volume 1 - 2024 (Sciences et Technologies)</option>
                                <option value="2">Volume 2 - 2024 (Recherche Interdisciplinaire)</option>
                                <option value="3">Volume 3 - 2024 (Numéro Spécial)</option>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="publish_page_start" class="form-label">Page de début</label>
                                    <input type="number" class="form-control" id="publish_page_start" name="page_start" min="1" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="publish_page_end" class="form-label">Page de fin</label>
                                    <input type="number" class="form-control" id="publish_page_end" name="page_end" min="1" required>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-success" id="confirmPublish">Publier</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test des formulaires corrigés initialisé');
            
            // Gestionnaires pour les boutons d'assignation
            document.querySelectorAll('.assign-btn').forEach(button => {
                button.addEventListener('click', function() {
                    console.log('🟡 Bouton assigner cliqué !');
                    const articleId = this.getAttribute('data-id');
                    document.getElementById('assign_article_id').value = articleId;

                    // Définir la date limite par défaut (dans 2 semaines)
                    const deadline = new Date();
                    deadline.setDate(deadline.getDate() + 14);
                    document.getElementById('assign_deadline').value = deadline.toISOString().split('T')[0];

                    // Ouvrir la modal
                    const modal = new bootstrap.Modal(document.getElementById('assignModal'));
                    modal.show();
                });
            });

            // Gestionnaires pour les boutons de publication
            document.querySelectorAll('.publish-btn').forEach(button => {
                button.addEventListener('click', function() {
                    console.log('🟢 Bouton publier cliqué !');
                    const articleId = this.getAttribute('data-id');
                    document.getElementById('publish_article_id').value = articleId;

                    // Ouvrir la modal
                    const modal = new bootstrap.Modal(document.getElementById('publishModal'));
                    modal.show();
                });
            });

            // Confirmation d'assignation
            document.getElementById('confirmAssign').addEventListener('click', function() {
                const form = document.getElementById('assignForm');
                const formData = new FormData(form);
                
                console.log('✅ Données d\'assignation:', {
                    article_id: formData.get('article_id'),
                    evaluator_id: formData.get('evaluator_id'),
                    deadline: formData.get('deadline'),
                    instructions: formData.get('instructions')
                });
                
                alert('Formulaire d\'assignation testé avec succès !');
            });

            // Confirmation de publication
            document.getElementById('confirmPublish').addEventListener('click', function() {
                const form = document.getElementById('publishForm');
                const formData = new FormData(form);

                // Validation des pages
                const pageStart = parseInt(document.getElementById('publish_page_start').value);
                const pageEnd = parseInt(document.getElementById('publish_page_end').value);

                if (pageEnd <= pageStart) {
                    alert('La page de fin doit être supérieure à la page de début');
                    return;
                }
                
                console.log('✅ Données de publication:', {
                    article_id: formData.get('article_id'),
                    volume_id: formData.get('volume_id'),
                    page_start: formData.get('page_start'),
                    page_end: formData.get('page_end')
                });
                
                alert('Formulaire de publication testé avec succès !');
            });
            
            // Test des événements de changement pour vérifier que les champs sont interactifs
            document.getElementById('assign_evaluator_id').addEventListener('change', function() {
                console.log('Évaluateur sélectionné:', this.value);
            });
            
            document.getElementById('assign_deadline').addEventListener('change', function() {
                console.log('Date limite modifiée:', this.value);
            });
            
            document.getElementById('assign_instructions').addEventListener('input', function() {
                console.log('Instructions modifiées:', this.value.length + ' caractères');
            });
            
            document.getElementById('publish_volume_id').addEventListener('change', function() {
                console.log('Volume sélectionné:', this.value);
            });
            
            document.getElementById('publish_page_start').addEventListener('input', function() {
                console.log('Page de début:', this.value);
            });
            
            document.getElementById('publish_page_end').addEventListener('input', function() {
                console.log('Page de fin:', this.value);
            });
        });
    </script>
</body>
</html>
