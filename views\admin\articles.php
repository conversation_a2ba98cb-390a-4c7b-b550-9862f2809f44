<?php include BASE_PATH . '/views/layout/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-alt"></i> Gestion des Articles</h2>
                <div class="btn-group">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignModal">
                        <i class="fas fa-user-plus"></i> Assigner Évaluateur
                    </button>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#publishModal">
                        <i class="fas fa-publish"></i> Publier Article
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="articlesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Titre</th>
                                    <th>Auteur</th>
                                    <th>Domaine</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($articles as $article): ?>
                                <tr class="article-row" data-id="<?= $article['id'] ?>">
                                    <td><?= $article['id'] ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($article['title']) ?></strong>
                                        <br><small class="text-muted"><?= substr($article['abstract'], 0, 100) ?>...</small>
                                    </td>
                                    <td><?= htmlspecialchars($article['firstname'] . ' ' . $article['lastname']) ?></td>
                                    <td><?= htmlspecialchars($article['domain_name'] ?? 'Non spécifié') ?></td>
                                    <td>
                                        <span class="badge status-<?= $article['status'] ?> status-badge">
                                            <?= ucfirst(str_replace('_', ' ', $article['status'])) ?>
                                        </span>
                                    </td>
                                    <td><?= date('d/m/Y', strtotime($article['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= BASE_URL ?>articles/view?id=<?= $article['id'] ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($article['status'] === 'soumis'): ?>
                                            <button class="btn btn-outline-warning assign-btn" 
                                                    data-id="<?= $article['id'] ?>">
                                                <i class="fas fa-user-plus"></i>
                                            </button>
                                            <?php endif; ?>
                                            <?php if ($article['status'] === 'accepte'): ?>
                                            <button class="btn btn-outline-success publish-btn" 
                                                    data-id="<?= $article['id'] ?>">
                                                <i class="fas fa-publish"></i>
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Animations et interactions
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'apparition des lignes
    const rows = document.querySelectorAll('.article-row');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>

<?php include BASE_PATH . '/views/layout/footer.php'; ?>