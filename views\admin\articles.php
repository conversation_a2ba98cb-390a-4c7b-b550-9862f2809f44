<?php include BASE_PATH . '/views/layout/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-alt"></i> Gestion des Articles</h2>
                <div class="btn-group">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#assignModal">
                        <i class="fas fa-user-plus"></i> Assigner Évaluateur
                    </button>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#publishModal">
                        <i class="fas fa-publish"></i> Publier Article
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="articlesTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Titre</th>
                                    <th>Auteur</th>
                                    <th>Domaine</th>
                                    <th>Statut</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($articles as $article): ?>
                                <tr class="article-row" data-id="<?= $article['id'] ?>">
                                    <td><?= $article['id'] ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($article['title']) ?></strong>
                                        <br><small class="text-muted"><?= substr($article['abstract'], 0, 100) ?>...</small>
                                    </td>
                                    <td><?= htmlspecialchars($article['firstname'] . ' ' . $article['lastname']) ?></td>
                                    <td><?= htmlspecialchars($article['domain_name'] ?? 'Non spécifié') ?></td>
                                    <td>
                                        <span class="badge status-<?= $article['status'] ?> status-badge">
                                            <?= ucfirst(str_replace('_', ' ', $article['status'])) ?>
                                        </span>
                                    </td>
                                    <td><?= date('d/m/Y', strtotime($article['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?= BASE_URL ?>articles/view?id=<?= $article['id'] ?>" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($article['status'] === 'soumis'): ?>
                                            <button class="btn btn-outline-warning assign-btn"
                                                    data-id="<?= $article['id'] ?>"
                                                    title="Assigner un évaluateur">
                                                <i class="fas fa-user-plus"></i> Assigner
                                            </button>
                                            <?php endif; ?>
                                            <?php if ($article['status'] === 'accepte'): ?>
                                            <button class="btn btn-outline-success publish-btn"
                                                    data-id="<?= $article['id'] ?>"
                                                    title="Publier l'article">
                                                <i class="fas fa-publish"></i> Publier
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour assigner un évaluateur -->
<div class="modal fade" id="assignModal" tabindex="-1" aria-labelledby="assignModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assignModalLabel">Assigner un Évaluateur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="assignForm">
                    <input type="hidden" id="assign_article_id" name="article_id">
                    <div class="mb-3">
                        <label for="evaluator_id" class="form-label">Sélectionner un évaluateur</label>
                        <select class="form-select" id="evaluator_id" name="evaluator_id" required>
                            <option value="">Choisir un évaluateur...</option>
                            <?php foreach ($evaluators as $evaluator): ?>
                                <option value="<?= $evaluator['id'] ?>">
                                    <?= htmlspecialchars($evaluator['firstname'] . ' ' . $evaluator['lastname']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="deadline" class="form-label">Date limite d'évaluation</label>
                        <input type="date" class="form-control" id="deadline" name="deadline" required>
                    </div>
                    <div class="mb-3">
                        <label for="instructions" class="form-label">Instructions pour l'évaluateur (optionnel)</label>
                        <textarea class="form-control" id="instructions" name="instructions" rows="3"
                                  placeholder="Instructions spécifiques pour cette évaluation..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmAssign">Assigner</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour publier un article -->
<div class="modal fade" id="publishModal" tabindex="-1" aria-labelledby="publishModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="publishModalLabel">Publier un Article</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="publishForm">
                    <input type="hidden" id="publish_article_id" name="article_id">
                    <div class="mb-3">
                        <label for="volume_id" class="form-label">Volume</label>
                        <select class="form-select" id="volume_id" name="volume_id" required>
                            <option value="">Sélectionner un volume...</option>
                            <?php foreach ($volumes as $volume): ?>
                                <option value="<?= $volume['id'] ?>">
                                    Volume <?= $volume['number'] ?> - <?= $volume['year'] ?> (<?= htmlspecialchars($volume['title']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="page_start" class="form-label">Page de début</label>
                                <input type="number" class="form-control" id="page_start" name="page_start" min="1" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="page_end" class="form-label">Page de fin</label>
                                <input type="number" class="form-control" id="page_end" name="page_end" min="1" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-success" id="confirmPublish">Publier</button>
            </div>
        </div>
    </div>
</div>

<script>
// Animations et interactions
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'apparition des lignes
    const rows = document.querySelectorAll('.article-row');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Gestionnaires pour les boutons d'assignation
    document.querySelectorAll('.assign-btn').forEach(button => {
        button.addEventListener('click', function() {
            console.log('Bouton assigner cliqué !');
            const articleId = this.getAttribute('data-id');
            document.getElementById('assign_article_id').value = articleId;

            // Définir la date limite par défaut (dans 2 semaines)
            const deadline = new Date();
            deadline.setDate(deadline.getDate() + 14);
            document.getElementById('deadline').value = deadline.toISOString().split('T')[0];

            // Ouvrir la modal
            const modal = new bootstrap.Modal(document.getElementById('assignModal'));
            modal.show();
        });
    });

    // Gestionnaires pour les boutons de publication
    document.querySelectorAll('.publish-btn').forEach(button => {
        button.addEventListener('click', function() {
            const articleId = this.getAttribute('data-id');
            document.getElementById('publish_article_id').value = articleId;

            // Ouvrir la modal
            const modal = new bootstrap.Modal(document.getElementById('publishModal'));
            modal.show();
        });
    });

    // Confirmation d'assignation
    document.getElementById('confirmAssign').addEventListener('click', function() {
        const form = document.getElementById('assignForm');
        const formData = new FormData(form);

        fetch('<?= BASE_URL ?>admin/assignEvaluator', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Évaluateur assigné avec succès !');
                location.reload();
            } else {
                alert('Erreur lors de l\'assignation');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de l\'assignation');
        });
    });

    // Confirmation de publication
    document.getElementById('confirmPublish').addEventListener('click', function() {
        const form = document.getElementById('publishForm');
        const formData = new FormData(form);

        // Validation des pages
        const pageStart = parseInt(document.getElementById('page_start').value);
        const pageEnd = parseInt(document.getElementById('page_end').value);

        if (pageEnd <= pageStart) {
            alert('La page de fin doit être supérieure à la page de début');
            return;
        }

        fetch('<?= BASE_URL ?>admin/publishArticle', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Article publié avec succès ! DOI: ' + data.doi);
                location.reload();
            } else {
                alert('Erreur lors de la publication');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            alert('Erreur lors de la publication');
        });
    });
});
</script>

<?php include BASE_PATH . '/views/layout/footer.php'; ?>