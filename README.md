# Journal Scientifique Universitaire

## Description
Application web complète pour la gestion d'un journal scientifique universitaire avec workflow éditorial complet.

## Fonctionnalités
- **Gestion des utilisateurs** : Auteurs, Évaluateurs, Éditeurs
- **Soumission d'articles** : Upload PDF, métadonnées
- **Workflow éditorial** : Soumis → Évaluation → Accepté → Publié
- **Système d'évaluation** : Peer review anonyme
- **Messagerie interne** : Communication anonymisée
- **Gestion des volumes** : Organisation par numéros
- **DOI local** : Attribution automatique
- **Recherche avancée** : Par mots-clés et domaines

## Installation

### Prérequis
- PHP 7.4+
- MySQL 5.7+
- Serveur web (Apache/Nginx)

### Configuration
1. Cloner le projet
2. Importer `donnees.sql` dans MySQL
3. Configurer `config/database.php` avec vos paramètres
4. <PERSON><PERSON><PERSON> le dossier `uploads/articles/` avec permissions d'écriture
5. Configurer le serveur web

### Compte par défaut
- Email: <EMAIL>
- Mot de passe: password

## Structure
```
/
├── config/          # Configuration
├── core/           # Classes de base MVC
├── controllers/    # Contrôleurs
├── models/         # Modèles de données
├── views/          # Vues et templates
├── assets/         # CSS, JS, images
├── uploads/        # Fichiers uploadés
└── donnees.sql     # Base de données
```

## Technologies
- **Backend** : PHP (POO, MVC)
- **Base de données** : MySQL
- **Frontend** : HTML5, CSS3, JavaScript, Bootstrap 5
- **Sécurité** : Hachage bcrypt, validation, protection CSRF

## Licence
Projet éducatif - Université