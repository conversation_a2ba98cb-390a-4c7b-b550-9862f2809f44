# Guide de test pour résoudre le problème des boutons de publication

## Problème identifié
Le bouton "publier un article" ne fonctionnait pas car :
1. Les modales Bootstrap n'existaient pas
2. Les gestionnaires d'événements JavaScript manquaient
3. Les routes pour les actions AJAX n'étaient pas définies

## Solutions apportées

### 1. Ajout des modales Bootstrap
- ✅ Modal `#publishModal` pour publier un article
- ✅ Modal `#assignModal` pour assigner un évaluateur
- ✅ Formulaires avec validation

### 2. Ajout des gestionnaires JavaScript
- ✅ Événements click pour les boutons `.publish-btn` et `.assign-btn`
- ✅ Validation des données avant envoi
- ✅ Appels AJAX vers le serveur

### 3. Ajout des routes manquantes
- ✅ Route `admin/assignEvaluator`
- ✅ Route `admin/publishArticle`

### 4. Correction du contrôleur
- ✅ Passage des évaluateurs et volumes depuis le contrôleur vers la vue

## Étapes de test

### Étape 1: Vérifier l'environnement
1. Ouvrez XAMPP et démarrez Apache et MySQL
2. Accédez à http://localhost/journal-scientifique/diagnostic.php
3. Vérifiez que toutes les vérifications sont vertes

### Étape 2: Préparer les données de test
1. Accédez à phpMyAdmin : http://localhost/phpmyadmin
2. Importez le fichier `donnees.sql` si ce n'est pas déjà fait
3. Exécutez http://localhost/journal-scientifique/insert_test_data.php

### Étape 3: Tester la fonctionnalité
1. Connectez-vous avec : <EMAIL> / password
2. Accédez à : http://localhost/journal-scientifique/admin/articles
3. Vous devriez voir un article avec statut "accepté"
4. Cliquez sur le bouton de publication (icône verte)
5. La modal devrait s'ouvrir avec les champs :
   - Sélection du volume
   - Page de début
   - Page de fin

### Étape 4: Test complet
1. Remplissez le formulaire dans la modal
2. Cliquez sur "Publier"
3. L'article devrait être publié avec un DOI généré
4. La page devrait se recharger avec le nouveau statut

## Fichiers modifiés

1. **views/admin/articles.php** - Ajout des modales et JavaScript
2. **core/Router.php** - Ajout des nouvelles routes
3. **controllers/AdminController.php** - Passage des données aux vues
4. **donnees.sql** - Ajout de données de test

## Test de validation

Pour tester rapidement si les boutons fonctionnent :
1. Ouvrez http://localhost/journal-scientifique/test_buttons.html
2. Cliquez sur le bouton "Publier"
3. La modal devrait s'ouvrir
4. Remplissez les champs et cliquez "Publier"
5. Vous devriez voir une alerte de confirmation

## Dépannage

### Problème spécifique : Bouton "Assigner évaluateur" ne fonctionne pas

**Causes possibles :**
1. **Pas d'articles avec statut "soumis"** - Le bouton n'apparaît que pour les articles soumis
2. **Erreurs JavaScript** - Vérifiez la console du navigateur
3. **Bootstrap non chargé** - Vérifiez que Bootstrap JS est inclus
4. **Sélecteurs CSS incorrects** - Vérifiez que les classes correspondent

**Solutions :**

1. **Vérifiez les données de test :**
   - Exécutez `http://localhost/journal-scientifique/check_and_fix.php`
   - Assurez-vous qu'il y a au moins un article avec statut "soumis"

2. **Testez les boutons isolément :**
   - Ouvrez `http://localhost/journal-scientifique/debug_buttons.html`
   - Vérifiez que les événements se déclenchent dans la console

3. **Vérifiez la console du navigateur :**
   - Appuyez sur F12 pour ouvrir les outils de développement
   - Allez dans l'onglet "Console"
   - Recherchez les erreurs JavaScript en rouge

4. **Vérifiez les éléments HTML :**
   - Dans les outils de développement, allez dans l'onglet "Elements"
   - Recherchez les boutons avec la classe `.assign-btn`
   - Vérifiez qu'ils ont l'attribut `data-id`

### Si les boutons ne réagissent toujours pas :
1. Ouvrez la console du navigateur (F12)
2. Vérifiez s'il y a des erreurs JavaScript
3. Vérifiez que Bootstrap est bien chargé
4. Vérifiez que les classes CSS sont correctes

### Si les modales ne s'ouvrent pas :
1. Vérifiez que Bootstrap JS est chargé
2. Vérifiez la console pour les erreurs
3. Vérifiez que les IDs des modales correspondent

### Si les appels AJAX échouent :
1. Vérifiez les routes dans core/Router.php
2. Vérifiez les méthodes dans AdminController.php
3. Vérifiez la console réseau du navigateur

### Fichiers de test créés :
- `debug_buttons.html` - Test isolé des boutons avec logging
- `check_and_fix.php` - Vérification et création des données de test
- `test_assign_button.php` - Test spécifique du bouton d'assignation

## Prochaines étapes

Une fois que les boutons fonctionnent :
1. Testez avec de vrais articles
2. Vérifiez la génération des DOI
3. Testez l'assignation d'évaluateurs
4. Vérifiez les permissions utilisateur
