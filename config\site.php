<?php
class SiteConfig {
    private static $instance = null;
    private $settings;
    
    private function __construct() {
        $this->settings = [
            'site_title' => 'Journal Scientifique Universitaire',
            'site_description' => 'Revue scientifique universitaire multidisciplinaire',
            'contact_email' => '<EMAIL>',
            'base_url' => 'http://localhost/journal-scientifique',
            'uploads_dir' => __DIR__ . '/../uploads/articles',
            'allowed_file_types' => ['pdf'],
            'max_file_size' => 10485760, // 10MB
            'default_timezone' => 'Europe/Paris'
        ];
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function get($setting) {
        return $this->settings[$setting] ?? null;
    }
    
    public function getAll() {
        return $this->settings;
    }
}
?>
