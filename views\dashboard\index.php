<?php include 'views/layout/header.php'; ?>

<div class="dashboard-header mb-4">
    <h1>Tableau de bord</h1>
    <p class="lead">Bienvenue, <?= $_SESSION['firstname'] . ' ' . $_SESSION['lastname'] ?></p>
</div>

<?php if ($role === 'auteur'): ?>
    <div class="row">
        <div class="col-md-4">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= $articlesCount ?></h3>
                    <p>Mes articles</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= $unreadMessages ?></h3>
                    <p>Messages non lus</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="text-center">
                <a href="<?= BASE_URL ?>articles/submit" class="btn btn-success btn-lg">
                    Soumettre un article
                </a>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <h3>Mes articles récents</h3>
        <?php if (!empty($myArticles)): ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Titre</th>
                            <th>Statut</th>
                            <th>Date de soumission</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_slice($myArticles, 0, 5) as $article): ?>
                            <tr>
                                <td><?= htmlspecialchars($article['title']) ?></td>
                                <td>
                                    <span class="badge status-<?= $article['status'] ?>">
                                        <?= ucfirst($article['status']) ?>
                                    </span>
                                </td>
                                <td><?= date('d/m/Y', strtotime($article['created_at'])) ?></td>
                                <td>
                                    <a href="<?= BASE_URL ?>articles/view?id=<?= $article['id'] ?>" class="btn btn-sm btn-primary">Voir</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p>Vous n'avez pas encore soumis d'articles.</p>
        <?php endif; ?>
    </div>

<?php elseif ($role === 'evaluateur'): ?>
    <div class="row">
        <div class="col-md-4">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= count($pendingEvaluations) ?></h3>
                    <p>Évaluations en attente</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= count($completedEvaluations) ?></h3>
                    <p>Évaluations terminées</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= $unreadMessages ?></h3>
                    <p>Messages non lus</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <h3>Évaluations en attente</h3>
        <?php if (!empty($pendingEvaluations)): ?>
            <?php foreach ($pendingEvaluations as $evaluation): ?>
                <div class="card mb-3">
                    <div class="card-body">
                        <h5><?= htmlspecialchars($evaluation['title']) ?></h5>
                        <p><?= substr(htmlspecialchars($evaluation['abstract']), 0, 200) ?>...</p>
                        <a href="<?= BASE_URL ?>evaluation/evaluate?id=<?= $evaluation['id'] ?>" class="btn btn-primary">Évaluer</a>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <p>Aucune évaluation en attente.</p>
        <?php endif; ?>
    </div>

<?php elseif ($role === 'editeur'): ?>
    <div class="row">
        <div class="col-md-3">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= isset($submittedArticles) ? count($submittedArticles) : 0 ?></h3>
                    <p>Articles soumis</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= isset($underReviewArticles) ? count($underReviewArticles) : 0 ?></h3>
                    <p>En évaluation</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= isset($acceptedArticles) ? count($acceptedArticles) : 0 ?></h3>
                    <p>Acceptés</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card dashboard-stats">
                <div class="card-body text-center">
                    <h3><?= isset($totalArticles) ? $totalArticles : 0 ?></h3>
                    <p>Total articles</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-4">
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Actions rapides</h5>
                    </div>
                    <div class="card-body">
                        <a href="<?= BASE_URL ?>admin/articles" class="btn btn-primary mb-2 d-block">Gérer les articles</a>
                        <a href="<?= BASE_URL ?>admin/volumes" class="btn btn-secondary mb-2 d-block">Gérer les volumes</a>
                        <a href="<?= BASE_URL ?>admin/users" class="btn btn-info d-block">Gérer les utilisateurs</a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Articles nécessitant une action</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($submittedArticles)): ?>
                            <?php foreach (array_slice($submittedArticles, 0, 3) as $article): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span><?= substr(htmlspecialchars($article['title']), 0, 30) ?>...</span>
                                    <a href="<?= BASE_URL ?>admin/articles" class="btn btn-sm btn-outline-primary">Voir</a>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p>Aucun article en attente.</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<?php include 'views/layout/footer.php'; ?>
