<?php
class DashboardController extends Controller {
    
    public function index() {
        $this->requireLogin();
        
        $role = $_SESSION['role'];
        $userId = $_SESSION['user_id'];
        
        $data = ['role' => $role];
        
        if ($role === 'auteur') {
            $articleModel = $this->loadModel('Article');
            $data['articlesCount'] = count($articleModel->getByAuthor($userId));
            $data['myArticles'] = $articleModel->getByAuthor($userId);
            $data['unreadMessages'] = 0; // À implémenter plus tard
            
        } elseif ($role === 'evaluateur') {
            // Données pour évaluateur
            $data['pendingEvaluations'] = [];
            $data['completedEvaluations'] = [];
            $data['unreadMessages'] = 0;
            
        } elseif ($role === 'editeur') {
            $articleModel = $this->loadModel('Article');
            
            // Toutes les données nécessaires pour l'éditeur
            $data['totalArticles'] = $articleModel->getTotalCount();
            $data['submittedArticles'] = $articleModel->getByStatus('soumis');
            $data['underReviewArticles'] = $articleModel->getByStatus('en_evaluation');
            $data['acceptedArticles'] = $articleModel->getByStatus('accepte');
            $data['publishedArticles'] = $articleModel->getByStatus('publie');
            $data['unreadMessages'] = 0;
        }
        
        $this->loadView('dashboard/index', $data);
    }
}
?>

