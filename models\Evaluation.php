<?php
class Evaluation extends Model {
    protected $table = 'evaluations';
    
    public function getByEvaluator($evaluatorId) {
        $stmt = $this->db->prepare("
            SELECT e.*, a.title, a.abstract, u.firstname, u.lastname 
            FROM evaluations e 
            JOIN articles a ON e.article_id = a.id 
            JOIN users u ON a.author_id = u.id 
            WHERE e.evaluator_id = ? 
            ORDER BY e.created_at DESC
        ");
        $stmt->execute([$evaluatorId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getByArticle($articleId) {
        $stmt = $this->db->prepare("
            SELECT e.*, u.firstname, u.lastname 
            FROM evaluations e 
            JOIN users u ON e.evaluator_id = u.id 
            WHERE e.article_id = ?
        ");
        $stmt->execute([$articleId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getPendingByEvaluator($evaluatorId) {
        $stmt = $this->db->prepare("
            SELECT e.*, a.title, a.abstract 
            FROM evaluations e 
            JOIN articles a ON e.article_id = a.id 
            WHERE e.evaluator_id = ? AND e.status = 'en_cours'
        ");
        $stmt->execute([$evaluatorId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function getCompletedByEvaluator($evaluatorId) {
        $stmt = $this->db->prepare("
            SELECT e.*, a.title 
            FROM evaluations e 
            JOIN articles a ON e.article_id = a.id 
            WHERE e.evaluator_id = ? AND e.status = 'terminee' 
            ORDER BY e.evaluation_date DESC
        ");
        $stmt->execute([$evaluatorId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
?>